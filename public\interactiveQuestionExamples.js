/**
 * Interactive Question Examples for Mathematics Assessment
 * These hardcoded examples demonstrate the interactive question types
 * before AI generation is implemented.
 */

const INTERACTIVE_QUESTION_EXAMPLES = {
  // Entry Level Interactive Questions (22 total questions)
  Entry: [
    // Drag-Drop Questions
    {
      id: 1,
      type: "drag-drop",
      topic: "fractions",
      question: "Match each fraction to its decimal equivalent.",
      config: {
        items: ["1/2", "1/4"],
        targets: ["0.5", "0.25"],
        correctPairs: [["1/2", "0.5"], ["1/4", "0.25"]]
      },
      correctAnswer: "All pairs matched correctly",
      points: 2,
      explanation: "1/2 = 0.5, 1/4 = 0.25"
    },
    {
      id: 2,
      type: "drag-drop",
      topic: "decimals",
      question: "Match each decimal to its position description.",
      config: {
        items: ["0.75", "0.25"],
        targets: ["Three-quarters", "One-quarter"],
        correctPairs: [["0.75", "Three-quarters"], ["0.25", "One-quarter"]]
      },
      correctAnswer: "All pairs matched correctly",
      points: 2,
      explanation: "0.75 is three-quarters, 0.25 is one-quarter"
    },
    {
      id: 3,
      type: "drag-drop",
      topic: "integers",
      question: "Match each negative number to its description.",
      config: {
        items: ["-3", "-1"],
        targets: ["Three units left of zero", "One unit left of zero"],
        correctPairs: [["-3", "Three units left of zero"], ["-1", "One unit left of zero"]]
      },
      correctAnswer: "All pairs matched correctly",
      points: 2,
      explanation: "-3 is three units to the left of zero, -1 is one unit to the left of zero"
    },

    // Drag and Drop Questions
    {
      id: 4,
      type: "drag-drop",
      topic: "fractions",
      question: "Match each fraction to its decimal equivalent.",
      dragDropConfig: {
        items: [
          { id: "frac1", text: "1/4" },
          { id: "frac2", text: "1/2" },
          { id: "frac3", text: "3/4" }
        ],
        zones: [
          { id: "dec1", label: "0.25" },
          { id: "dec2", label: "0.50" },
          { id: "dec3", label: "0.75" }
        ]
      },
      correctAnswer: '{"0.25":"1/4","0.50":"1/2","0.75":"3/4"}',
      points: 2,
      explanation: "1/4 = 0.25, 1/2 = 0.50, 3/4 = 0.75"
    },
    {
      id: 5,
      type: "drag-drop",
      topic: "percentages",
      question: "Match each percentage to its fraction equivalent.",
      dragDropConfig: {
        items: [
          { id: "perc1", text: "25%" },
          { id: "perc2", text: "50%" },
          { id: "perc3", text: "75%" }
        ],
        zones: [
          { id: "frac1", label: "1/4" },
          { id: "frac2", label: "1/2" },
          { id: "frac3", label: "3/4" }
        ]
      },
      correctAnswer: '{"1/4":"25%","1/2":"50%","3/4":"75%"}',
      points: 2,
      explanation: "25% = 1/4, 50% = 1/2, 75% = 3/4"
    },

    // Number Line Questions (Additional)
    {
      id: 6,
      type: "drag-drop",
      topic: "percentages",
      question: "Match each percentage to its decimal equivalent.",
      config: {
        items: ["25%", "75%"],
        targets: ["0.25", "0.75"],
        correctPairs: [["25%", "0.25"], ["75%", "0.75"]]
      },
      correctAnswer: "All pairs matched correctly",
      points: 2,
      explanation: "25% = 0.25, 75% = 0.75"
    },
    {
      id: 7,
      type: "area-model",
      topic: "fractions",
      question: "Shade 3/4 of the fraction bar.",
      areaModelConfig: {
        fractionBars: [
          { segments: 4, label: "Quarters Bar" }
        ],
        geometricShapes: []
      },
      correctAnswer: '{"fractionBars":{"Quarters Bar":{"totalSegments":4,"shadedSegments":[0,1,2],"fraction":"3/4"}},"geometricShapes":[]}',
      points: 2,
      explanation: "To show 3/4, shade 3 out of 4 equal parts."
    },
    {
      id: 8,
      type: "area-model",
      topic: "geometry",
      question: "Click on the shapes that represent 1/2 of a whole.",
      areaModelConfig: {
        fractionBars: [],
        geometricShapes: [
          { type: "rectangle", width: 100, height: 50, label: "Rectangle" },
          { type: "circle", width: 80, height: 80, label: "Circle" },
          { type: "triangle", width: 90, height: 80, label: "Triangle" }
        ]
      },
      correctAnswer: '{"fractionBars":{},"geometricShapes":[{"type":"rectangle","label":"Rectangle","shaded":true}]}',
      points: 2,
      explanation: "Any shape can represent 1/2 when half of it is shaded."
    }
  ],

  // Level 1 Interactive Questions (13 total questions)
  Level1: [
    {
      id: 1,
      type: "drag-drop",
      topic: "advancedArithmetic",
      question: "Match each decimal to its position description.",
      config: {
        items: ["-2.5", "1.5"],
        targets: ["Between -3 and -2", "Between 1 and 2"],
        correctPairs: [["-2.5", "Between -3 and -2"], ["1.5", "Between 1 and 2"]]
      },
      correctAnswer: "All pairs matched correctly",
      points: 2,
      explanation: "-2.5 is between -3 and -2, 1.5 is between 1 and 2"
    },
    {
      id: 2,
      type: "drag-drop",
      topic: "algebraicExpressions",
      question: "Match each algebraic expression to its simplified form.",
      dragDropConfig: {
        items: [
          { id: "expr1", text: "2x + 3x" },
          { id: "expr2", text: "4y - 2y" },
          { id: "expr3", text: "3a + 2a - a" }
        ],
        zones: [
          { id: "simp1", label: "5x" },
          { id: "simp2", label: "2y" },
          { id: "simp3", label: "4a" }
        ]
      },
      correctAnswer: '{"5x":"2x + 3x","2y":"4y - 2y","4a":"3a + 2a - a"}',
      points: 2,
      explanation: "Combine like terms: 2x + 3x = 5x, 4y - 2y = 2y, 3a + 2a - a = 4a"
    },
    {
      id: 3,
      type: "drag-drop",
      topic: "fractionsDecimals",
      question: "Match each percentage to its fraction equivalent.",
      dragDropConfig: {
        items: [
          { id: "perc1", text: "50%" },
          { id: "perc2", text: "25%" },
          { id: "perc3", text: "75%" }
        ],
        zones: [
          { id: "frac1", label: "1/2" },
          { id: "frac2", label: "1/4" },
          { id: "frac3", label: "3/4" }
        ]
      },
      correctAnswer: '{"1/2":"50%","1/4":"25%","3/4":"75%"}',
      points: 2,
      explanation: "50% = 1/2, 25% = 1/4, 75% = 3/4"
    }
  ],

  // GCSE Part 1 Interactive Questions (7 total questions)
  GCSEPart1: [
    {
      id: 1,
      type: "drag-drop",
      topic: "numberOperations",
      question: "Match each square root to its value.",
      config: {
        items: ["√9", "√25"],
        targets: ["3", "5"],
        correctPairs: [["√9", "3"], ["√25", "5"]]
      },
      correctAnswer: "All pairs matched correctly",
      points: 2,
      explanation: "√9 = 3 because 3² = 9, √25 = 5 because 5² = 25"
    },
    {
      id: 2,
      type: "drag-drop",
      topic: "algebra",
      question: "Match each equation to its solution.",
      dragDropConfig: {
        items: [
          { id: "eq1", text: "2x = 10" },
          { id: "eq2", text: "x + 3 = 8" },
          { id: "eq3", text: "3x - 1 = 14" }
        ],
        zones: [
          { id: "sol1", label: "x = 5" },
          { id: "sol2", label: "x = 5" },
          { id: "sol3", label: "x = 5" }
        ]
      },
      correctAnswer: '{"x = 5":"2x = 10"}',
      points: 2,
      explanation: "Solve each equation by isolating x."
    }
  ],

  // GCSE Part 2 Interactive Questions (10 total questions)
  GCSEPart2: [
    {
      id: 1,
      type: "drag-drop",
      topic: "complexCalculations",
      question: "Match each calculation to its result.",
      config: {
        items: ["2.5 × 1.6", "3.2 × 1.25"],
        targets: ["4.0", "4.0"],
        correctPairs: [["2.5 × 1.6", "4.0"], ["3.2 × 1.25", "4.0"]]
      },
      correctAnswer: "All pairs matched correctly",
      points: 2,
      explanation: "2.5 × 1.6 = 4.0, 3.2 × 1.25 = 4.0"
    },
    {
      id: 2,
      type: "drag-drop",
      topic: "algebra",
      question: "Match each equation to its solution.",
      config: {
        items: ["3x + 2 = 11", "2x + 5 = 11"],
        targets: ["x = 3", "x = 3"],
        correctPairs: [["3x + 2 = 11", "x = 3"], ["2x + 5 = 11", "x = 3"]]
      },
      correctAnswer: "All pairs matched correctly",
      points: 2,
      explanation: "3x + 2 = 11 → x = 3; 2x + 5 = 11 → x = 3"
    }
  ]
};

// Export for use in the assessment system
if (typeof module !== 'undefined' && module.exports) {
  module.exports = INTERACTIVE_QUESTION_EXAMPLES;
} else if (typeof window !== 'undefined') {
  window.INTERACTIVE_QUESTION_EXAMPLES = INTERACTIVE_QUESTION_EXAMPLES;
}
