<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Drag and Drop Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .question {
            margin-bottom: 30px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
        
        .question h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 18px;
        }
        
        .drag-drop-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }
        
        .items-container, .zones-container {
            padding: 20px;
            border: 2px solid #333;
            border-radius: 8px;
            background: white;
        }
        
        .items-container h4, .zones-container h4 {
            margin: 0 0 15px 0;
            color: #333;
            text-align: center;
        }
        
        .draggable-item {
            background: #4CAF50;
            color: white;
            padding: 15px 20px;
            margin: 10px 0;
            border-radius: 6px;
            cursor: grab;
            user-select: none;
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            border: 2px solid #45a049;
            transition: all 0.2s ease;
        }
        
        .draggable-item:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .draggable-item.dragging {
            opacity: 0.7;
            transform: rotate(5deg) scale(1.05);
            cursor: grabbing;
            z-index: 1000;
        }
        
        .drop-zone {
            background: #f0f0f0;
            border: 3px dashed #999;
            border-radius: 6px;
            padding: 20px;
            margin: 10px 0;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: #666;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .drop-zone.drag-over {
            border-color: #2196F3;
            background: #e3f2fd;
            border-style: solid;
            transform: scale(1.02);
        }
        
        .drop-zone.filled {
            background: #e8f5e8;
            border-color: #4CAF50;
            border-style: solid;
            color: #2e7d32;
            font-weight: bold;
        }
        
        .reset-btn {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 20px;
        }
        
        .reset-btn:hover {
            background: #ff5252;
        }
        
        .feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .feedback.correct {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .feedback.incorrect {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Drag and Drop Test</h1>
        <p>This is a minimal test to verify drag-and-drop functionality works correctly.</p>
        
        <div class="question">
            <h3>Match each fraction to its decimal equivalent:</h3>
            
            <div class="drag-drop-container">
                <div class="items-container">
                    <h4>Fractions (Drag these)</h4>
                    <div class="draggable-item" draggable="true" data-value="1/2">1/2</div>
                    <div class="draggable-item" draggable="true" data-value="1/4">1/4</div>
                    <div class="draggable-item" draggable="true" data-value="3/4">3/4</div>
                    <div class="draggable-item" draggable="true" data-value="1/5">1/5</div>
                </div>
                
                <div class="zones-container">
                    <h4>Decimals (Drop here)</h4>
                    <div class="drop-zone" data-accept="1/2">0.5</div>
                    <div class="drop-zone" data-accept="1/4">0.25</div>
                    <div class="drop-zone" data-accept="3/4">0.75</div>
                    <div class="drop-zone" data-accept="1/5">0.2</div>
                </div>
            </div>
            
            <button class="reset-btn" onclick="resetQuestion()">Reset</button>
            <div id="feedback" class="feedback" style="display: none;"></div>
        </div>
    </div>

    <script>
        let draggedElement = null;
        let matches = {};
        
        // Add event listeners to draggable items
        document.querySelectorAll('.draggable-item').forEach(item => {
            item.addEventListener('dragstart', handleDragStart);
            item.addEventListener('dragend', handleDragEnd);
        });
        
        // Add event listeners to drop zones
        document.querySelectorAll('.drop-zone').forEach(zone => {
            zone.addEventListener('dragover', handleDragOver);
            zone.addEventListener('dragenter', handleDragEnter);
            zone.addEventListener('dragleave', handleDragLeave);
            zone.addEventListener('drop', handleDrop);
        });
        
        function handleDragStart(e) {
            draggedElement = e.target;
            e.target.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/html', e.target.outerHTML);
            console.log('Drag started:', e.target.textContent);
        }
        
        function handleDragEnd(e) {
            e.target.classList.remove('dragging');
            console.log('Drag ended:', e.target.textContent);
        }
        
        function handleDragOver(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
        }
        
        function handleDragEnter(e) {
            e.preventDefault();
            e.target.classList.add('drag-over');
        }
        
        function handleDragLeave(e) {
            e.target.classList.remove('drag-over');
        }
        
        function handleDrop(e) {
            e.preventDefault();
            e.target.classList.remove('drag-over');
            
            if (draggedElement) {
                const draggedValue = draggedElement.dataset.value;
                const acceptedValue = e.target.dataset.accept;
                
                console.log('Drop:', draggedValue, 'onto', acceptedValue);
                
                // Check if this zone already has an item
                if (e.target.classList.contains('filled')) {
                    showFeedback('This zone is already filled!', 'incorrect');
                    return;
                }
                
                // Remove item from any previous zone
                Object.keys(matches).forEach(zoneId => {
                    if (matches[zoneId] === draggedValue) {
                        const oldZone = document.querySelector(`[data-accept="${zoneId}"]`);
                        if (oldZone) {
                            oldZone.classList.remove('filled');
                            oldZone.textContent = oldZone.dataset.accept;
                        }
                        delete matches[zoneId];
                    }
                });
                
                // Add to new zone
                matches[acceptedValue] = draggedValue;
                e.target.classList.add('filled');
                e.target.textContent = `${draggedValue} = ${acceptedValue}`;
                
                // Hide the dragged item
                draggedElement.style.display = 'none';
                
                // Check if correct
                if (draggedValue === acceptedValue) {
                    showFeedback(`Correct! ${draggedValue} = ${acceptedValue}`, 'correct');
                } else {
                    showFeedback(`Try again! ${draggedValue} ≠ ${acceptedValue}`, 'incorrect');
                }
                
                checkCompletion();
            }
        }
        
        function showFeedback(message, type) {
            const feedback = document.getElementById('feedback');
            feedback.textContent = message;
            feedback.className = `feedback ${type}`;
            feedback.style.display = 'block';
            
            setTimeout(() => {
                feedback.style.display = 'none';
            }, 3000);
        }
        
        function checkCompletion() {
            const totalItems = document.querySelectorAll('.draggable-item').length;
            const matchedItems = Object.keys(matches).length;
            
            if (matchedItems === totalItems) {
                const correctMatches = Object.keys(matches).filter(zone => matches[zone] === zone).length;
                if (correctMatches === totalItems) {
                    showFeedback('🎉 Perfect! All matches are correct!', 'correct');
                } else {
                    showFeedback(`${correctMatches}/${totalItems} correct. Keep trying!`, 'incorrect');
                }
            }
        }
        
        function resetQuestion() {
            // Reset matches
            matches = {};
            
            // Reset drop zones
            document.querySelectorAll('.drop-zone').forEach(zone => {
                zone.classList.remove('filled', 'drag-over');
                zone.textContent = zone.dataset.accept;
            });
            
            // Show all draggable items
            document.querySelectorAll('.draggable-item').forEach(item => {
                item.style.display = 'block';
                item.classList.remove('dragging');
            });
            
            // Hide feedback
            document.getElementById('feedback').style.display = 'none';
            
            console.log('Question reset');
        }
    </script>
</body>
</html>
