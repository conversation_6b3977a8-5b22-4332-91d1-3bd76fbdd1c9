<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Question Clarity Improvements</title>
    <link rel="stylesheet" href="mathInteractive.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #f9fafb;
        }
        
        .test-section {
            background: white;
            padding: 2rem;
            margin: 2rem 0;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            color: #1547bb;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .question-text {
            font-size: 1.1rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 1rem;
        }
        
        .answer-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            margin: 0.5rem 0;
        }
        
        .multiple-choice {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin: 1rem 0;
        }
        
        .choice-option {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .choice-option:hover {
            border-color: #1547bb;
            background: #f8f9ff;
        }
        
        .drag-drop-container {
            display: flex;
            gap: 2rem;
            margin: 1rem 0;
        }
        
        .drag-items, .drop-zones {
            flex: 1;
        }
        
        .drag-item {
            padding: 0.75rem;
            background: #f3f4f6;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            margin: 0.5rem 0;
            cursor: move;
            text-align: center;
        }
        
        .drop-zone {
            padding: 0.75rem;
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            margin: 0.5rem 0;
            min-height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <h1>Mathematics Assessment - Question Clarity Test</h1>
    <p>This page tests the improved question clarity and instructions for different question types.</p>

    <!-- Test 1: Multiple Choice Question -->
    <div class="test-section">
        <div class="test-title">Test 1: Multiple Choice Question (Previously "step by step")</div>
        <div class="question-text">What is 15 + 28?</div>
        
        <!-- Instructions will be added here by JavaScript -->
        <div id="question-instructions-1" class="question-instructions">
            <p class="instruction-text">Select the correct answer from the options below.</p>
        </div>
        
        <div class="multiple-choice">
            <div class="choice-option">A) 41</div>
            <div class="choice-option">B) 43</div>
            <div class="choice-option">C) 45</div>
            <div class="choice-option">D) 47</div>
        </div>
        
        <p><strong>Explanation:</strong> To calculate 15 + 28: Add the ones (5 + 8 = 13), write down 3 and carry 1. Add the tens (1 + 2 + 1 = 4). The answer is 43.</p>
    </div>

    <!-- Test 2: Numeric Input Question -->
    <div class="test-section">
        <div class="test-title">Test 2: Numeric Input Question (Previously "step by step")</div>
        <div class="question-text">Calculate 84 - 37</div>
        
        <div id="question-instructions-2" class="question-instructions">
            <p class="instruction-text">Enter your answer as a number (e.g., 42 or 3.5). Do not include units unless specified.</p>
        </div>
        
        <input type="number" class="answer-input" placeholder="Enter your answer">
        
        <p><strong>Explanation:</strong> Subtract: 84 - 37 = 47. You can work this out by breaking it down: 84 - 30 = 54, then 54 - 7 = 47.</p>
    </div>

    <!-- Test 3: Short Answer Question -->
    <div class="test-section">
        <div class="test-title">Test 3: Short Answer Question (Previously "step by step")</div>
        <div class="question-text">Solve the equation: 2x + 3 = 11. Enter your answer as x = [number]</div>
        
        <div id="question-instructions-3" class="question-instructions">
            <p class="instruction-text">Enter your answer in the text box. For equations, use the format shown in the question (e.g., x = 4).</p>
        </div>
        
        <input type="text" class="answer-input" placeholder="x = ">
        
        <p><strong>Explanation:</strong> To solve 2x + 3 = 11: Step 1: Subtract 3 from both sides: 2x + 3 - 3 = 11 - 3, which gives 2x = 8. Step 2: Divide both sides by 2: 2x ÷ 2 = 8 ÷ 2, which gives x = 4.</p>
    </div>

    <!-- Test 4: Drag and Drop Question -->
    <div class="test-section">
        <div class="test-title">Test 4: Drag and Drop Question</div>
        <div class="question-text">Match each fraction to its decimal equivalent.</div>
        
        <div id="question-instructions-4" class="question-instructions">
            <p class="instruction-text">Drag each item from the left to match it with the correct answer on the right.</p>
        </div>
        
        <div class="drag-drop-container">
            <div class="drag-items">
                <div class="drag-item">1/2</div>
                <div class="drag-item">1/4</div>
                <div class="drag-item">3/4</div>
            </div>
            <div class="drop-zones">
                <div class="drop-zone">0.25</div>
                <div class="drop-zone">0.50</div>
                <div class="drop-zone">0.75</div>
            </div>
        </div>
        
        <p><strong>Explanation:</strong> 1/4 = 0.25, 1/2 = 0.50, 3/4 = 0.75</p>
    </div>

    <div class="test-section">
        <h2>Summary of Improvements</h2>
        <ul>
            <li><strong>Removed confusing "step by step" text</strong> from questions that only need final answers</li>
            <li><strong>Added clear instructions</strong> for each question type explaining what format is expected</li>
            <li><strong>Converted problematic questions</strong> to appropriate types (multiple-choice, numeric, short-answer)</li>
            <li><strong>Provided detailed explanations</strong> showing the step-by-step process in the feedback</li>
            <li><strong>Visual styling</strong> makes instructions stand out with blue accent border</li>
        </ul>
    </div>
</body>
</html>
