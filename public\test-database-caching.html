<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Question Caching System Test</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: #f9fafb;
            line-height: 1.6;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: linear-gradient(135deg, #1547bb, #3b82f6);
            color: white;
            border-radius: 12px;
        }
        
        .test-section {
            background: white;
            padding: 2rem;
            margin: 2rem 0;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }
        
        .test-button {
            background: #1547bb;
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .test-button:hover:not(:disabled) {
            background: #121c41;
            transform: translateY(-1px);
        }
        
        .test-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .status-button {
            background: #10b981;
        }
        
        .status-button:hover:not(:disabled) {
            background: #059669;
        }
        
        .clear-button {
            background: #ef4444;
        }
        
        .clear-button:hover:not(:disabled) {
            background: #dc2626;
        }
        
        .results {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1.5rem;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .results pre {
            margin: 0;
            white-space: pre-wrap;
            font-size: 0.9rem;
            color: #374151;
        }
        
        .loading {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            padding: 1.5rem;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #1547bb;
        }
        
        .feature-card h4 {
            color: #1547bb;
            margin: 0 0 0.5rem 0;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .metric-card {
            text-align: center;
            padding: 1rem;
            background: #f0f9ff;
            border-radius: 8px;
            border: 1px solid #bfdbfe;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: 600;
            color: #1e40af;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: #6b7280;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗄️ Database Question Caching System</h1>
        <p>Test the new 7-day persistent database caching for mathematics and digital skills questions</p>
    </div>

    <div class="test-section">
        <h2>🎯 Key Features</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>🚀 Dual-Layer Caching</h4>
                <p>In-memory cache for immediate access, database cache for persistence across server restarts</p>
            </div>
            <div class="feature-card">
                <h4>⏰ 7-Day Expiry</h4>
                <p>Questions automatically expire after 7 days, ensuring fresh content while reducing API calls</p>
            </div>
            <div class="feature-card">
                <h4>🧹 Automatic Cleanup</h4>
                <p>Scheduled cleanup every 24 hours removes expired cache entries automatically</p>
            </div>
            <div class="feature-card">
                <h4>📊 Cache Analytics</h4>
                <p>Comprehensive monitoring of cache hit rates, storage usage, and performance metrics</p>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 Cache Testing</h2>
        <p>Test the database caching system with these interactive controls:</p>
        
        <div class="button-grid">
            <button class="test-button status-button" onclick="getCacheStatus()">
                📊 Get Cache Status
            </button>
            <button class="test-button" onclick="testMathQuestions()">
                🧮 Test Math Questions
            </button>
            <button class="test-button" onclick="testDigitalQuestions()">
                💻 Test Digital Questions
            </button>
            <button class="test-button clear-button" onclick="clearInMemoryCache()">
                🗑️ Clear In-Memory Cache
            </button>
            <button class="test-button clear-button" onclick="clearDatabaseCache()">
                🗄️ Clear Database Cache
            </button>
            <button class="test-button" onclick="cleanupExpiredCache()">
                🧹 Cleanup Expired Cache
            </button>
        </div>
        
        <div id="results" class="results" style="display: none;">
            <pre id="results-content"></pre>
        </div>
    </div>

    <div class="test-section">
        <h2>📈 Performance Metrics</h2>
        <div id="metrics" class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value" id="cache-hits">-</div>
                <div class="metric-label">Cache Hits</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="cache-misses">-</div>
                <div class="metric-label">Cache Misses</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="db-entries">-</div>
                <div class="metric-label">DB Cache Entries</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="memory-entries">-</div>
                <div class="metric-label">Memory Cache Entries</div>
            </div>
        </div>
        <button class="test-button" onclick="updateMetrics()">🔄 Refresh Metrics</button>
    </div>

    <script>
        function showResults(content) {
            const resultsDiv = document.getElementById('results');
            const resultsContent = document.getElementById('results-content');
            resultsContent.textContent = JSON.stringify(content, null, 2);
            resultsDiv.style.display = 'block';
        }

        function showLoading(button) {
            const originalText = button.innerHTML;
            button.innerHTML = '<div class="loading"><div class="spinner"></div>Loading...</div>';
            button.disabled = true;
            return () => {
                button.innerHTML = originalText;
                button.disabled = false;
            };
        }

        async function getCacheStatus() {
            const button = event.target;
            const stopLoading = showLoading(button);
            
            try {
                const response = await fetch('/api/cache/database/status');
                const data = await response.json();
                showResults(data);
                updateMetricsFromData(data);
            } catch (error) {
                showResults({ error: error.message });
            } finally {
                stopLoading();
            }
        }

        async function testMathQuestions() {
            const button = event.target;
            const stopLoading = showLoading(button);
            
            try {
                const response = await fetch('/api/math-assessments/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        level: 'Entry',
                        userData: { name: 'Test User' }
                    })
                });
                const data = await response.json();
                showResults({
                    message: 'Math questions generated/retrieved',
                    questionCount: data.questions?.length || 0,
                    level: data.level,
                    cached: data.cached || false
                });
            } catch (error) {
                showResults({ error: error.message });
            } finally {
                stopLoading();
            }
        }

        async function testDigitalQuestions() {
            const button = event.target;
            const stopLoading = showLoading(button);
            
            try {
                const response = await fetch('/api/digital-skills-assessments/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        level: 'EntryLevel2',
                        userData: { name: 'Test User' }
                    })
                });
                const data = await response.json();
                showResults({
                    message: 'Digital skills questions generated/retrieved',
                    questionCount: data.questions?.length || 0,
                    level: data.level,
                    cached: data.cached || false
                });
            } catch (error) {
                showResults({ error: error.message });
            } finally {
                stopLoading();
            }
        }

        async function clearInMemoryCache() {
            const button = event.target;
            const stopLoading = showLoading(button);
            
            try {
                const response = await fetch('/api/math-assessments/cache/clear', {
                    method: 'POST'
                });
                const data = await response.json();
                showResults(data);
            } catch (error) {
                showResults({ error: error.message });
            } finally {
                stopLoading();
            }
        }

        async function clearDatabaseCache() {
            const button = event.target;
            const stopLoading = showLoading(button);
            
            try {
                const response = await fetch('/api/cache/database/clear', {
                    method: 'POST'
                });
                const data = await response.json();
                showResults(data);
            } catch (error) {
                showResults({ error: error.message });
            } finally {
                stopLoading();
            }
        }

        async function cleanupExpiredCache() {
            const button = event.target;
            const stopLoading = showLoading(button);
            
            try {
                const response = await fetch('/api/cache/database/cleanup', {
                    method: 'POST'
                });
                const data = await response.json();
                showResults(data);
            } catch (error) {
                showResults({ error: error.message });
            } finally {
                stopLoading();
            }
        }

        async function updateMetrics() {
            try {
                const [performanceResponse, cacheResponse] = await Promise.all([
                    fetch('/api/math-assessments/performance'),
                    fetch('/api/cache/database/status')
                ]);
                
                const performanceData = await performanceResponse.json();
                const cacheData = await cacheResponse.json();
                
                updateMetricsFromData({ performance: performanceData, cache: cacheData });
            } catch (error) {
                console.error('Failed to update metrics:', error);
            }
        }

        function updateMetricsFromData(data) {
            if (data.performance?.metrics) {
                document.getElementById('cache-hits').textContent = data.performance.metrics.cacheHits || 0;
                document.getElementById('cache-misses').textContent = data.performance.metrics.cacheMisses || 0;
            }
            
            if (data.summary || data.cache?.summary) {
                const summary = data.summary || data.cache.summary;
                document.getElementById('db-entries').textContent = summary.totalEntries || 0;
            }
            
            if (data.inMemoryCache || data.cache?.inMemoryCache) {
                const memCache = data.inMemoryCache || data.cache.inMemoryCache;
                const total = (memCache.mathQuestions || 0) + (memCache.digitalSkillsQuestions || 0);
                document.getElementById('memory-entries').textContent = total;
            }
        }

        // Load initial metrics
        updateMetrics();
    </script>
</body>
</html>
