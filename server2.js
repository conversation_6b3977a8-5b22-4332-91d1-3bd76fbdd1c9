// ============================================================================
// STANDALONE MATHEMATICS ASSESSMENT SERVER
// ============================================================================
// This is a self-contained server focused specifically on mathematics assessment
// functionality, extracted from the main server for easier debugging and testing.

// Import required modules
require('dotenv').config();
const express = require('express');
const bodyParser = require('body-parser');
const path = require('path');
const OpenAI = require('openai');
const admin = require('firebase-admin');
const cors = require('cors');
const NodeCache = require('node-cache');

// Initialize Express app
const app = express();
const port = process.env.PORT || 3003; // Use different port to avoid conflicts

// Configure middleware
app.use(cors());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, 'public')));

// Import Firebase service account key
const serviceAccount = require('./service_account.json');

// Initialize Firebase Admin SDK
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/"
});

// Initialize Firebase services
const firestore = admin.firestore();

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

console.log('OpenAI API Key:', process.env.OPENAI_API_KEY ? 'Configured' : 'Missing');

// ============================================================================
// MATHEMATICS ASSESSMENT CONFIGURATION & CONSTANTS
// ============================================================================

// Mathematics Question Cache System
const mathQuestionCache = new Map(); // In-memory cache for immediate access
const CACHE_EXPIRY_TIME = 60 * 60 * 1000; // 1 hour for in-memory cache
const DATABASE_CACHE_EXPIRY_TIME = 7 * 24 * 60 * 60 * 1000; // 7 days for database cache
const CACHE_MAX_SIZE = 200;
const API_TIMEOUT_THRESHOLD = 35000; // 35 seconds
const MAX_RETRY_ATTEMPTS = 3; // Increased from 1 to 3 for better reliability
const RETRY_TIMEOUTS = [35000, 45000, 60000]; // Progressive timeout values for retries
const RETRY_DELAYS = [1000, 2000, 4000]; // Exponential backoff delays in milliseconds
const BATCH_SIZE_THRESHOLD = 20; // Break requests into batches if more than 20 questions
const MAX_BATCH_SIZE = 12; // Maximum questions per batch

// Priority queue system for request management
const REQUEST_PRIORITY = {
  ASSESSMENT: 1,    // Highest priority - active user assessments
  CACHE_WARMUP: 2,  // Lower priority - background cache warming
  PRELOAD: 3        // Lowest priority - startup preloading
};

// Active request tracking
const activeRequests = {
  assessment: new Set(),
  cacheWarmup: new Set(),
  preload: new Set()
};

// Request queue management
let isProcessingHighPriority = false;
const PRELOAD_CACHE_ON_STARTUP = true;
const CONCURRENT_PRELOAD_LIMIT = 2;
const MAX_CONCURRENT_ASSESSMENTS = 5; // Allow multiple concurrent user assessments

// Performance metrics tracking
const performanceMetrics = {
  totalRequests: 0,
  cacheHits: 0,
  cacheMisses: 0,
  fallbackUsage: 0,
  averageGenerationTime: 0,
  apiTimeouts: 0,
  retryAttempts: 0,
  successfulRetries: 0,
  consecutiveFailures: 0,
  lastFailureTime: null,
  retrySuccessRate: 0,
  digitalSkillsTimeouts: 0,
  mathTimeouts: 0
};

// Function to reset performance metrics to ensure proper numeric values
function resetPerformanceMetrics() {
  performanceMetrics.totalRequests = 0;
  performanceMetrics.cacheHits = 0;
  performanceMetrics.cacheMisses = 0;
  performanceMetrics.fallbackUsage = 0;
  performanceMetrics.averageGenerationTime = 0;
  performanceMetrics.apiTimeouts = 0;
  performanceMetrics.retryAttempts = 0;
  performanceMetrics.successfulRetries = 0;
  performanceMetrics.consecutiveFailures = 0;
  performanceMetrics.lastFailureTime = null;
  performanceMetrics.retrySuccessRate = 0;
  performanceMetrics.digitalSkillsTimeouts = 0;
  performanceMetrics.mathTimeouts = 0;
}

// Utility function for exponential backoff delay
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Update retry success rate calculation
function updateRetrySuccessRate() {
  if (performanceMetrics.retryAttempts > 0) {
    performanceMetrics.retrySuccessRate =
      (performanceMetrics.successfulRetries / performanceMetrics.retryAttempts * 100).toFixed(2);
  }
}

// Track consecutive failures for alerting
function trackFailure(type = 'general') {
  performanceMetrics.consecutiveFailures++;
  performanceMetrics.lastFailureTime = new Date().toISOString();

  // Log alert for consecutive failures
  if (performanceMetrics.consecutiveFailures >= 5) {
    console.error(`🚨 ALERT: ${performanceMetrics.consecutiveFailures} consecutive API failures detected for ${type}!`);
    console.error(`🚨 Last failure at: ${performanceMetrics.lastFailureTime}`);
  }
}

// Reset consecutive failures on success
function resetConsecutiveFailures() {
  if (performanceMetrics.consecutiveFailures > 0) {
    console.log(`✅ API success - resetting ${performanceMetrics.consecutiveFailures} consecutive failures`);
    performanceMetrics.consecutiveFailures = 0;
    performanceMetrics.lastFailureTime = null;
  }
}

// Priority queue management functions
function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function addActiveRequest(requestId, priority) {
  switch (priority) {
    case REQUEST_PRIORITY.ASSESSMENT:
      activeRequests.assessment.add(requestId);
      isProcessingHighPriority = true;
      console.log(`🚨 HIGH PRIORITY: Assessment request ${requestId} started`);
      break;
    case REQUEST_PRIORITY.CACHE_WARMUP:
      activeRequests.cacheWarmup.add(requestId);
      break;
    case REQUEST_PRIORITY.PRELOAD:
      activeRequests.preload.add(requestId);
      break;
  }
}

function removeActiveRequest(requestId, priority) {
  switch (priority) {
    case REQUEST_PRIORITY.ASSESSMENT:
      activeRequests.assessment.delete(requestId);
      if (activeRequests.assessment.size === 0) {
        isProcessingHighPriority = false;
        console.log(`✅ All high priority assessment requests completed`);
      }
      break;
    case REQUEST_PRIORITY.CACHE_WARMUP:
      activeRequests.cacheWarmup.delete(requestId);
      break;
    case REQUEST_PRIORITY.PRELOAD:
      activeRequests.preload.delete(requestId);
      break;
  }
}

// Wait for high priority requests to complete before proceeding with lower priority
async function waitForHighPriorityRequests(currentPriority) {
  if (currentPriority === REQUEST_PRIORITY.ASSESSMENT) {
    return; // Assessment requests have highest priority, never wait
  }

  // Only wait if there are actual assessment requests running
  // Don't wait for cache warmup or preload requests
  if (activeRequests.assessment.size > 0) {
    console.log(`⏳ Priority ${currentPriority} request waiting for ${activeRequests.assessment.size} high priority assessment requests...`);

    // Wait with timeout to prevent infinite waiting
    let waitTime = 0;
    const maxWaitTime = 30000; // Maximum 30 seconds wait

    while (activeRequests.assessment.size > 0 && waitTime < maxWaitTime) {
      await delay(1000);
      waitTime += 1000;
    }

    if (waitTime >= maxWaitTime) {
      console.warn(`⚠️ Priority ${currentPriority} request timed out waiting for assessment requests`);
    }
  }
}

function getActiveRequestsStatus() {
  return {
    assessment: activeRequests.assessment.size,
    cacheWarmup: activeRequests.cacheWarmup.size,
    preload: activeRequests.preload.size,
    isProcessingHighPriority
  };
}

// Generate questions in batches for better reliability
async function generateQuestionsInBatches(level, studentLevel, questionSpecs, createPromptFn, makeAPICallFn, validateFn, priority = REQUEST_PRIORITY.ASSESSMENT) {
  const totalQuestions = questionSpecs.count;

  // If total questions is below threshold, generate all at once
  if (totalQuestions <= BATCH_SIZE_THRESHOLD) {
    console.log(`📦 Generating ${totalQuestions} questions in single batch (below threshold)`);
    const prompt = createPromptFn(level, questionSpecs, studentLevel);
    const completion = await makeAPICallFn(prompt, level);
    const content = completion.choices[0].message.content;
    const parsedQuestions = parseAIQuestionsResponse(content, level);
    return validateFn(parsedQuestions, level);
  }

  // Calculate batch configuration
  const batchSize = Math.min(MAX_BATCH_SIZE, Math.ceil(totalQuestions / Math.ceil(totalQuestions / MAX_BATCH_SIZE)));
  const numBatches = Math.ceil(totalQuestions / batchSize);

  console.log(`📦 Generating ${totalQuestions} questions in ${numBatches} batches of ~${batchSize} questions each`);

  const allQuestions = [];
  const topics = questionSpecs.topics;

  for (let batchIndex = 0; batchIndex < numBatches; batchIndex++) {
    const startIndex = batchIndex * batchSize;
    const endIndex = Math.min(startIndex + batchSize, totalQuestions);
    const batchQuestionCount = endIndex - startIndex;

    // Create a subset of topics for this batch
    const topicsPerBatch = Math.ceil(topics.length / numBatches);
    const batchTopicStart = (batchIndex * topicsPerBatch) % topics.length;
    const batchTopics = [];
    for (let i = 0; i < topicsPerBatch && batchTopics.length < topics.length; i++) {
      const topicIndex = (batchTopicStart + i) % topics.length;
      if (!batchTopics.includes(topics[topicIndex])) {
        batchTopics.push(topics[topicIndex]);
      }
    }

    // Create batch-specific question specs
    const batchSpecs = {
      ...questionSpecs,
      count: batchQuestionCount,
      topics: batchTopics,
      maxScore: batchQuestionCount * 2 // Assuming 2 points per question
    };

    console.log(`📦 Batch ${batchIndex + 1}/${numBatches}: ${batchQuestionCount} questions, topics: ${batchTopics.join(', ')}`);

    try {
      const prompt = createPromptFn(level, batchSpecs, studentLevel);
      const completion = await makeAPICallFn(prompt, level);
      const content = completion.choices[0].message.content;
      const parsedQuestions = parseAIQuestionsResponse(content, level);
      const validatedQuestions = validateFn(parsedQuestions, level);

      // Adjust question IDs to be sequential across batches
      const adjustedQuestions = validatedQuestions.map((q, index) => ({
        ...q,
        id: startIndex + index + 1
      }));

      allQuestions.push(...adjustedQuestions);
      console.log(`✅ Batch ${batchIndex + 1} completed: ${adjustedQuestions.length} valid questions`);

      // Small delay between batches to avoid rate limiting (shorter for high priority)
      if (batchIndex < numBatches - 1) {
        const delayTime = priority === REQUEST_PRIORITY.ASSESSMENT ? 200 : 500;
        await delay(delayTime);
      }

    } catch (error) {
      console.error(`❌ Batch ${batchIndex + 1} failed: ${error.message}`);
      // Continue with other batches, we'll handle insufficient questions later
    }
  }

  console.log(`📦 Batch generation complete: ${allQuestions.length}/${totalQuestions} questions generated`);
  return allQuestions;
}

// Get question specifications for each level
function getMathQuestionSpecs(level) {
  const specs = {
    'Entry': {
      count: 22,
      timeLimit: 30 * 60, // 30 minutes
      passingScore: 24,
      maxScore: 44,
      topics: ['arithmetic', 'fractions', 'percentages', 'basicAlgebra', 'measurement', 'dataHandling']
    },
    'Level1': {
      count: 13,
      timeLimit: 30 * 60, // 30 minutes
      passingScore: 16,
      maxScore: 26,
      topics: ['advancedArithmetic', 'fractionsDecimals', 'percentagesRatio', 'algebraicExpressions', 'geometry', 'statistics']
    },
    'GCSEPart1': {
      count: 7,
      timeLimit: 15 * 60, // 15 minutes
      passingScore: 5,
      maxScore: 10,
      topics: ['numberOperations', 'algebraicManipulation', 'geometricReasoning', 'fractionalCalculations']
    },
    'GCSEPart2': {
      count: 10,
      timeLimit: 20 * 60, // 20 minutes
      passingScore: 8,
      maxScore: 20,
      topics: ['complexCalculations', 'statisticalAnalysis', 'trigonometry', 'advancedAlgebra', 'problemSolving']
    }
  };

  return specs[level] || specs['Entry'];
}

// Get time limit for assessment level
function getMathTimeLimit(level) {
  return getMathQuestionSpecs(level).timeLimit;
}

// Get passing score for assessment level
function getMathPassingScore(level) {
  return getMathQuestionSpecs(level).passingScore;
}

// Get maximum score for assessment level
function getMathMaxScore(level) {
  return getMathQuestionSpecs(level).maxScore;
}

// ============================================================================
// DIGITAL SKILLS ASSESSMENT CONFIGURATION
// ============================================================================

// Get question specifications for each digital skills level
function getDigitalSkillsQuestionSpecs(level) {
  const specs = {
    'EntryLevel2': {
      count: 15,
      timeLimit: 25 * 60, // 25 minutes
      passingScore: 12,
      maxScore: 30,
      topics: ['computerBasics', 'mouseKeyboard', 'basicOperations', 'fileManagement', 'basicSafety']
    },
    'EntryLevel2Plus': {
      count: 18,
      timeLimit: 30 * 60, // 30 minutes
      passingScore: 15,
      maxScore: 36,
      topics: ['laptopDesktop', 'applications', 'internetSafety', 'emailBasics', 'digitalCitizenship']
    },
    'Level1': {
      count: 20,
      timeLimit: 35 * 60, // 35 minutes
      passingScore: 18,
      maxScore: 40,
      topics: ['microsoftApps', 'onlineBanking', 'cloudStorage', 'digitalIdentity', 'internetSkills']
    },
    'Level2': {
      count: 22,
      timeLimit: 40 * 60, // 40 minutes
      passingScore: 20,
      maxScore: 44,
      topics: ['advancedFormatting', 'spreadsheets', 'presentations', 'workplaceSkills', 'collaboration']
    },
    'EntryLevel3': {
      count: 16,
      timeLimit: 30 * 60, // 30 minutes
      passingScore: 14,
      maxScore: 32,
      topics: ['operatingSystems', 'emailProficiency', 'onlineTransactions', 'digitalSafety', 'troubleshooting']
    },
    'ICDLLevel2': {
      count: 25,
      timeLimit: 45 * 60, // 45 minutes
      passingScore: 22,
      maxScore: 50,
      topics: ['timedExam', 'wordAdvanced', 'excelAdvanced', 'powerpointAdvanced', 'employmentSkills']
    },
    'ICDLLevel3': {
      count: 30,
      timeLimit: 50 * 60, // 50 minutes
      passingScore: 26,
      maxScore: 60,
      topics: ['advancedMicrosoft', 'itCareers', 'higherEducation', 'professionalSkills', 'certification']
    }
  };

  return specs[level] || specs['EntryLevel2'];
}

// Get time limit for digital skills assessment level
function getDigitalSkillsTimeLimit(level) {
  return getDigitalSkillsQuestionSpecs(level).timeLimit;
}

// Get passing score for digital skills assessment level
function getDigitalSkillsPassingScore(level) {
  return getDigitalSkillsQuestionSpecs(level).passingScore;
}

// Get maximum score for digital skills assessment level
function getDigitalSkillsMaxScore(level) {
  return getDigitalSkillsQuestionSpecs(level).maxScore;
}

// ============================================================================
// CACHE MANAGEMENT FUNCTIONS
// ============================================================================

function getCacheKey(level, studentLevel) {
  return `${level}_${studentLevel || 'default'}`;
}

function getCachedQuestions(level, studentLevel) {
  const key = getCacheKey(level, studentLevel);
  const cached = mathQuestionCache.get(key);

  if (cached && Date.now() - cached.timestamp < CACHE_EXPIRY_TIME) {
    performanceMetrics.cacheHits++;
    console.log(`Cache hit for ${key} - questions served from cache`);
    return cached.questions;
  }

  if (cached) {
    mathQuestionCache.delete(key); // Remove expired cache
    console.log(`Cache expired for ${key} - removing from cache`);
  }

  performanceMetrics.cacheMisses++;
  return null;
}

function setCachedQuestions(level, studentLevel, questions) {
  const key = getCacheKey(level, studentLevel);

  // Implement LRU cache by removing oldest entries if cache is full
  if (mathQuestionCache.size >= CACHE_MAX_SIZE) {
    const oldestKey = mathQuestionCache.keys().next().value;
    mathQuestionCache.delete(oldestKey);
    console.log(`Cache full - removed oldest entry: ${oldestKey}`);
  }

  mathQuestionCache.set(key, {
    questions: questions,
    timestamp: Date.now()
  });

  console.log(`Cached questions for ${key} - cache size: ${mathQuestionCache.size}`);
}



// ============================================================================
// DATABASE QUESTION CACHING SYSTEM
// ============================================================================

/**
 * Get questions from database cache
 * @param {string} type - 'math' or 'digital'
 * @param {string} level - Assessment level
 * @param {string} studentLevel - Student level (optional)
 * @returns {Promise<Array|null>} Cached questions or null if not found/expired
 */
async function getDatabaseCachedQuestions(type, level, studentLevel = null) {
  try {
    const cacheKey = `${type}_${level}_${studentLevel || 'default'}`;
    console.log(`Checking database cache for: ${cacheKey}`);

    const cacheDoc = await firestore
      .collection('questionCache')
      .doc(cacheKey)
      .get();

    if (!cacheDoc.exists) {
      console.log(`No database cache found for: ${cacheKey}`);
      return null;
    }

    const cacheData = cacheDoc.data();
    const cacheAge = Date.now() - cacheData.timestamp.toDate().getTime();

    if (cacheAge > DATABASE_CACHE_EXPIRY_TIME) {
      console.log(`Database cache expired for ${cacheKey}, age: ${Math.round(cacheAge / (1000 * 60 * 60 * 24))} days`);
      // Delete expired cache
      await firestore.collection('questionCache').doc(cacheKey).delete();
      return null;
    }

    console.log(`Database cache hit for ${cacheKey}, age: ${Math.round(cacheAge / (1000 * 60 * 60))} hours`);

    // Deserialize questions before returning
    const deserializedQuestions = deserializeQuestionsFromFirestore(cacheData.questions);
    return deserializedQuestions;

  } catch (error) {
    console.warn(`Database cache retrieval failed for ${type}_${level}:`, error.message);
    return null;
  }
}

/**
 * Serialize questions for Firestore storage
 * Converts complex nested objects to JSON strings to avoid Firestore limitations
 */
function serializeQuestionsForFirestore(questions) {
  return questions.map(question => {
    const serialized = { ...question };

    // Convert complex nested objects to JSON strings
    if (question.config && typeof question.config === 'object') {
      serialized.config = JSON.stringify(question.config);
    }

    if (question.interactiveData && typeof question.interactiveData === 'object') {
      serialized.interactiveData = JSON.stringify(question.interactiveData);
    }

    if (question.options && Array.isArray(question.options)) {
      // Arrays are generally OK in Firestore, but ensure they're simple
      serialized.options = question.options.map(opt =>
        typeof opt === 'object' ? JSON.stringify(opt) : opt
      );
    }

    // Handle any other complex nested objects
    Object.keys(serialized).forEach(key => {
      const value = serialized[key];
      if (value && typeof value === 'object' && !Array.isArray(value) && key !== 'timestamp') {
        // Check if it's a complex object that might cause issues
        try {
          JSON.stringify(value);
          // If it's complex, serialize it
          if (Object.keys(value).length > 0 && typeof Object.values(value)[0] === 'object') {
            serialized[key] = JSON.stringify(value);
          }
        } catch (e) {
          // If it can't be stringified, convert to string
          serialized[key] = String(value);
        }
      }
    });

    return serialized;
  });
}

/**
 * Deserialize questions from Firestore storage
 * Converts JSON strings back to objects
 */
function deserializeQuestionsFromFirestore(questions) {
  return questions.map(question => {
    const deserialized = { ...question };

    // Convert JSON strings back to objects
    if (typeof question.config === 'string') {
      try {
        deserialized.config = JSON.parse(question.config);
      } catch (e) {
        console.warn('Failed to parse config:', e.message);
      }
    }

    if (typeof question.interactiveData === 'string') {
      try {
        deserialized.interactiveData = JSON.parse(question.interactiveData);
      } catch (e) {
        console.warn('Failed to parse interactiveData:', e.message);
      }
    }

    // Handle other serialized objects
    Object.keys(deserialized).forEach(key => {
      const value = deserialized[key];
      if (typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
        try {
          deserialized[key] = JSON.parse(value);
        } catch (e) {
          // If parsing fails, keep as string
        }
      }
    });

    return deserialized;
  });
}

/**
 * Store questions in database cache
 * @param {string} type - 'math' or 'digital'
 * @param {string} level - Assessment level
 * @param {string} studentLevel - Student level (optional)
 * @param {Array} questions - Questions to cache
 * @param {Object} metadata - Additional metadata
 */
async function setDatabaseCachedQuestions(type, level, studentLevel = null, questions, metadata = {}) {
  try {
    const cacheKey = `${type}_${level}_${studentLevel || 'default'}`;
    console.log(`Storing questions in database cache: ${cacheKey}`);

    // Serialize questions to handle complex nested objects
    const serializedQuestions = serializeQuestionsForFirestore(questions);

    const cacheData = {
      questions: serializedQuestions,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      type: type,
      level: level,
      studentLevel: studentLevel,
      questionCount: questions.length,
      generatedAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + DATABASE_CACHE_EXPIRY_TIME).toISOString(),
      ...metadata
    };

    await firestore
      .collection('questionCache')
      .doc(cacheKey)
      .set(cacheData);

    console.log(`Successfully cached ${questions.length} questions in database for: ${cacheKey}`);

    // Also store in in-memory cache for immediate access
    if (type === 'math') {
      setCachedQuestions(level, studentLevel, questions);
    }

  } catch (error) {
    console.error(`Failed to store questions in database cache for ${type}_${level}:`, error.message);
  }
}

/**
 * Clear expired database cache entries
 */
async function cleanupExpiredDatabaseCache() {
  try {
    console.log('Starting database cache cleanup...');

    const snapshot = await firestore
      .collection('questionCache')
      .get();

    const expiredDocs = [];
    const currentTime = Date.now();

    snapshot.forEach(doc => {
      const data = doc.data();
      if (data.timestamp) {
        const cacheAge = currentTime - data.timestamp.toDate().getTime();
        if (cacheAge > DATABASE_CACHE_EXPIRY_TIME) {
          expiredDocs.push(doc.id);
        }
      }
    });

    if (expiredDocs.length > 0) {
      console.log(`Deleting ${expiredDocs.length} expired cache entries`);
      const batch = firestore.batch();

      expiredDocs.forEach(docId => {
        batch.delete(firestore.collection('questionCache').doc(docId));
      });

      await batch.commit();
      console.log(`Successfully deleted ${expiredDocs.length} expired cache entries`);
    } else {
      console.log('No expired cache entries found');
    }

  } catch (error) {
    console.error('Database cache cleanup failed:', error.message);
  }
}

function updateAverageGenerationTime(generationTime) {
  // Ensure generationTime is a number
  const numericGenerationTime = typeof generationTime === 'number' ? generationTime : 0;

  if (performanceMetrics.totalRequests === 1) {
    performanceMetrics.averageGenerationTime = numericGenerationTime;
  } else {
    // Ensure averageGenerationTime is a number
    const currentAverage = typeof performanceMetrics.averageGenerationTime === 'number'
      ? performanceMetrics.averageGenerationTime
      : 0;

    performanceMetrics.averageGenerationTime =
      (currentAverage * (performanceMetrics.totalRequests - 1) + numericGenerationTime) /
      performanceMetrics.totalRequests;
  }
}

function getPerformanceMetrics() {
  // Ensure all metrics are numbers
  const safeMetrics = {
    totalRequests: Number(performanceMetrics.totalRequests) || 0,
    cacheHits: Number(performanceMetrics.cacheHits) || 0,
    cacheMisses: Number(performanceMetrics.cacheMisses) || 0,
    fallbackUsage: Number(performanceMetrics.fallbackUsage) || 0,
    averageGenerationTime: Number(performanceMetrics.averageGenerationTime) || 0,
    apiTimeouts: Number(performanceMetrics.apiTimeouts) || 0,
    retryAttempts: Number(performanceMetrics.retryAttempts) || 0,
    successfulRetries: Number(performanceMetrics.successfulRetries) || 0,
    consecutiveFailures: Number(performanceMetrics.consecutiveFailures) || 0,
    digitalSkillsTimeouts: Number(performanceMetrics.digitalSkillsTimeouts) || 0,
    mathTimeouts: Number(performanceMetrics.mathTimeouts) || 0
  };

  const cacheHitRate = safeMetrics.totalRequests > 0
    ? (safeMetrics.cacheHits / safeMetrics.totalRequests * 100).toFixed(2)
    : 0;

  const retrySuccessRate = safeMetrics.retryAttempts > 0
    ? (safeMetrics.successfulRetries / safeMetrics.retryAttempts * 100).toFixed(2)
    : 0;

  const fallbackRate = safeMetrics.totalRequests > 0
    ? (safeMetrics.fallbackUsage / safeMetrics.totalRequests * 100).toFixed(2)
    : 0;

  const timeoutRate = safeMetrics.totalRequests > 0
    ? (safeMetrics.apiTimeouts / safeMetrics.totalRequests * 100).toFixed(2)
    : 0;

  return {
    ...safeMetrics,
    cacheHitRate: `${cacheHitRate}%`,
    retrySuccessRate: `${retrySuccessRate}%`,
    fallbackRate: `${fallbackRate}%`,
    timeoutRate: `${timeoutRate}%`,
    cacheSize: mathQuestionCache.size,
    lastFailureTime: performanceMetrics.lastFailureTime,
    healthStatus: safeMetrics.consecutiveFailures >= 5 ? 'CRITICAL' :
                  safeMetrics.consecutiveFailures >= 3 ? 'WARNING' : 'HEALTHY',
    activeRequests: getActiveRequestsStatus()
  };
}

// ============================================================================
// AI QUESTION GENERATION FUNCTIONS
// ============================================================================

// Enhanced AI prompt for high-quality mathematics question generation
function createOptimizedMathQuestionPrompt(level, questionSpecs, studentLevel) {
  const levelConfig = {
    'Entry': {
      description: 'Entry Level Mathematics - Foundation skills for adult learners',
      topics: {
        arithmetic: { weight: 30, description: 'Multi-step calculations, word problems, estimation, mental math strategies, order of operations, practical contexts (shopping, time, money)' },
        fractions: { weight: 20, description: 'Simple fractions (halves, quarters, thirds), basic operations' },
        percentages: { weight: 15, description: 'Common percentages (10%, 25%, 50%), percentage of amounts' },
        measurement: { weight: 15, description: 'Length, weight, capacity, time - basic conversions' },
        basicAlgebra: { weight: 10, description: 'Simple number patterns, basic substitution' },
        dataHandling: { weight: 10, description: 'Reading simple charts, basic statistics (mean, mode)' }
      }
    },
    'Level1': {
      description: 'Level 1 Mathematics - Building on foundation skills',
      topics: {
        advancedArithmetic: { weight: 25, description: 'Multi-step problem solving, estimation and approximation, mental calculation strategies, inverse operations, number patterns, practical applications' },
        fractionsDecimals: { weight: 20, description: 'Converting between fractions/decimals, operations' },
        percentagesRatio: { weight: 20, description: 'Percentage increase/decrease, ratios, proportions' },
        algebraicExpressions: { weight: 15, description: 'Simple equations, substitution, rearranging formulae' },
        geometry: { weight: 10, description: 'Area, perimeter, basic shapes, angles' },
        statistics: { weight: 10, description: 'Interpreting data, probability basics' }
      }
    },
    'GCSEPart1': {
      description: 'GCSE Part 1 - Non-calculator mathematics',
      topics: {
        numberOperations: { weight: 30, description: 'Mental arithmetic, fractions, percentages without calculator' },
        algebraicManipulation: { weight: 30, description: 'Expanding brackets, factoring, solving equations' },
        geometricReasoning: { weight: 25, description: 'Angle properties, similarity, basic proofs' },
        fractionalCalculations: { weight: 15, description: 'Complex fraction operations, mixed numbers' }
      }
    },
    'GCSEPart2': {
      description: 'GCSE Part 2 - Calculator mathematics',
      topics: {
        complexCalculations: { weight: 25, description: 'Advanced calculations using calculator' },
        statisticalAnalysis: { weight: 25, description: 'Data analysis, correlation, probability distributions' },
        trigonometry: { weight: 25, description: 'Sin, cos, tan, solving triangles' },
        advancedAlgebra: { weight: 15, description: 'Quadratic equations, simultaneous equations' },
        problemSolving: { weight: 10, description: 'Multi-step real-world problems' }
      }
    }
  };

  const config = levelConfig[level] || levelConfig['Entry'];
  const topics = Object.keys(config.topics).map(topic => 
    `${topic}: ${config.topics[topic].description} (${config.topics[topic].weight}% weight)`
  ).join('\n');

  return `Generate ${questionSpecs.count} high-quality mathematics questions for ${config.description}.

LEVEL: ${level}
STUDENT TYPE: ${studentLevel || 'adult-learner'}
QUESTION COUNT: ${questionSpecs.count}
MAX SCORE: ${questionSpecs.maxScore}

TOPIC DISTRIBUTION:
${topics}

REQUIREMENTS:
- Each question must have: id, type, topic, question, options (for multiple-choice), correctAnswer, points, explanation
- Use UK mathematical terminology (maths, brackets, anticlockwise)
- Questions should be practical and relevant to adult learners
- Include variety: multiple-choice, numeric-input, short-answer, drag-drop
- Ensure progressive difficulty within the level
- Points should total approximately ${questionSpecs.maxScore}
- Make questions clear and unambiguous
- Prioritize interactive drag-and-drop questions for better engagement

ARITHMETIC QUESTION GUIDELINES:
- AVOID simple "What is X + Y?" format questions
- Use real-world contexts: shopping, time, money, measurements, recipes
- Include multi-step problems requiring planning and strategy
- Test estimation and approximation skills
- Include inverse thinking: "What number plus 17 equals 45?"
- Use comparison questions: "Which is larger: 3×8 or 5×5?"
- Include pattern recognition in number sequences
- Test mental math strategies and shortcuts
- Use practical scenarios that require arithmetic reasoning

QUESTION TYPES:
- multiple-choice: 4 options, one correct answer
- numeric: Numerical answer required
- short-answer: Brief mathematical expression
- drag-drop: Interactive matching/ordering questions with draggable items and drop zones

EXAMPLE FORMATS:

Multiple Choice:
{
  "id": 1,
  "type": "multiple-choice",
  "topic": "arithmetic",
  "question": "Sarah buys 3 items costing £4.75, £2.30, and £6.95. She pays with a £20 note. How much change should she receive?",
  "options": ["£5.00", "£6.00", "£7.00", "£8.00"],
  "correctAnswer": "£6.00",
  "points": 2,
  "explanation": "Total cost: £4.75 + £2.30 + £6.95 = £14.00. Change: £20.00 - £14.00 = £6.00"
}

Drag-and-Drop:
{
  "id": 2,
  "type": "drag-drop",
  "topic": "fractions",
  "question": "Match each fraction to its decimal equivalent",
  "config": {
    "items": ["1/2", "1/4", "3/4", "1/5"],
    "targets": ["0.5", "0.25", "0.75", "0.2"],
    "correctPairs": [["1/2", "0.5"], ["1/4", "0.25"], ["3/4", "0.75"], ["1/5", "0.2"]]
  },
  "correctAnswer": "All pairs matched correctly",
  "points": 3,
  "explanation": "1/2 = 0.5, 1/4 = 0.25, 3/4 = 0.75, 1/5 = 0.2"
}

Return ONLY a valid JSON array of ${questionSpecs.count} questions.`;
}

// Helper function to make OpenAI API calls with enhanced retry logic and exponential backoff
async function makeOpenAICallWithRetry(prompt, level, attempt = 1) {
  const timeoutValue = RETRY_TIMEOUTS[attempt - 1] || API_TIMEOUT_THRESHOLD;
  const startTime = Date.now();

  console.log(`🔄 Mathematics API call attempt ${attempt}/${MAX_RETRY_ATTEMPTS} for ${level} (timeout: ${timeoutValue}ms)`);

  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('API timeout')), timeoutValue);
  });

  const apiPromise = openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      {
        role: "system",
        content: `You are an expert mathematics assessment designer specializing in UK educational standards. Your role is to create high-quality, educationally valuable mathematics questions for adult learners.

CRITICAL REQUIREMENTS:
- Return ONLY valid JSON array format
- No additional text, explanations, or markdown
- Each question must have all required fields
- Use UK mathematical terminology throughout
- Ensure questions are appropriate for the specified level`
      },
      {
        role: "user",
        content: prompt
      }
    ],
    max_tokens: 4000,
    temperature: 0.7
  });

  try {
    const completion = await Promise.race([apiPromise, timeoutPromise]);
    const responseTime = Date.now() - startTime;
    console.log(`✅ Mathematics API call successful in ${responseTime}ms (attempt ${attempt})`);

    // Reset consecutive failures on success
    resetConsecutiveFailures();

    return completion;
  } catch (error) {
    const responseTime = Date.now() - startTime;
    console.error(`❌ Mathematics API call failed in ${responseTime}ms (attempt ${attempt}): ${error.message}`);

    if (error.message === 'API timeout') {
      performanceMetrics.apiTimeouts++;
      performanceMetrics.mathTimeouts++;
      trackFailure('mathematics');
    }

    if (attempt < MAX_RETRY_ATTEMPTS) {
      performanceMetrics.retryAttempts++;
      const delayMs = RETRY_DELAYS[attempt - 1] || 1000;
      console.log(`⏳ Retrying mathematics API call for ${level} in ${delayMs}ms (attempt ${attempt + 1}/${MAX_RETRY_ATTEMPTS})`);

      // Apply exponential backoff delay
      await delay(delayMs);

      return makeOpenAICallWithRetry(prompt, level, attempt + 1);
    }

    // Track final failure
    trackFailure('mathematics');
    throw error;
  }
}

// Parse AI response with multiple strategies
function parseAIQuestionsResponse(content, level) {
  console.log(`Parsing AI response for ${level}...`);

  // Strategy 1: Direct JSON parse
  try {
    const questions = JSON.parse(content);
    if (Array.isArray(questions) && questions.length > 0) {
      console.log(`✅ Direct JSON parse successful - ${questions.length} questions`);
      return questions;
    }
  } catch (error) {
    console.log('Direct JSON parse failed, trying extraction...');
  }

  // Strategy 2: Extract JSON from markdown or mixed content
  const jsonMatch = content.match(/\[[\s\S]*\]/);
  if (jsonMatch) {
    try {
      const questions = JSON.parse(jsonMatch[0]);
      if (Array.isArray(questions) && questions.length > 0) {
        console.log(`✅ JSON extraction successful - ${questions.length} questions`);
        return questions;
      }
    } catch (error) {
      console.log('JSON extraction failed');
    }
  }

  // Strategy 3: Clean and retry
  const cleanedContent = content
    .replace(/```json/g, '')
    .replace(/```/g, '')
    .replace(/^\s*[\w\s]*:\s*/, '')
    .trim();

  try {
    const questions = JSON.parse(cleanedContent);
    if (Array.isArray(questions) && questions.length > 0) {
      console.log(`✅ Cleaned JSON parse successful - ${questions.length} questions`);
      return questions;
    }
  } catch (error) {
    console.log('Cleaned JSON parse failed');
  }

  console.error('All parsing strategies failed');
  return null;
}

// Validate and normalize questions
function validateQuestions(questions, level) {
  const questionSpecs = getMathQuestionSpecs(level);
  const validQuestions = [];

  for (const question of questions) {
    // Validate required fields
    if (!question.id || !question.type || !question.topic || !question.question || !question.correctAnswer) {
      console.warn('Skipping invalid question - missing required fields:', question);
      continue;
    }

    // Normalize question structure
    const normalizedQuestion = {
      id: parseInt(question.id) || validQuestions.length + 1,
      type: question.type,
      topic: question.topic,
      question: question.question,
      correctAnswer: String(question.correctAnswer),
      points: parseInt(question.points) || 2,
      explanation: question.explanation || 'No explanation provided'
    };

    // Add options for multiple-choice questions
    if (question.type === 'multiple-choice' && question.options) {
      normalizedQuestion.options = question.options.map(opt => String(opt));
    }

    // Add config for drag-drop questions
    if (question.type === 'drag-drop' && question.config) {
      normalizedQuestion.config = question.config;
    }

    validQuestions.push(normalizedQuestion);
  }

  console.log(`Validated ${validQuestions.length}/${questions.length} questions for ${level}`);
  return validQuestions;
}

// Generate mathematics questions using AI with caching and performance optimization
async function generateMathematicsQuestions(level, studentLevel, priority = REQUEST_PRIORITY.ASSESSMENT) {
  const startTime = Date.now();
  const requestId = generateRequestId();

  performanceMetrics.totalRequests++;
  addActiveRequest(requestId, priority);

  try {
    console.log(`Generating mathematics questions for ${level} level (Priority: ${priority}, Request: ${requestId})...`);

    // Check in-memory cache first (fastest)
    const cachedQuestions = getCachedQuestions(level, studentLevel);
    if (cachedQuestions) {
      const generationTime = Date.now() - startTime;
      console.log(`Questions served from in-memory cache in ${generationTime}ms`);
      updateAverageGenerationTime(generationTime);
      removeActiveRequest(requestId, priority);
      return cachedQuestions;
    }

    // Check database cache second (avoid API calls)
    const databaseCachedQuestions = await getDatabaseCachedQuestions('math', level, studentLevel);
    if (databaseCachedQuestions) {
      const generationTime = Date.now() - startTime;
      console.log(`Questions served from database cache in ${generationTime}ms`);
      updateAverageGenerationTime(generationTime);

      // Store in in-memory cache for faster future access
      setCachedQuestions(level, studentLevel, databaseCachedQuestions);

      removeActiveRequest(requestId, priority);
      return databaseCachedQuestions;
    }

    // Wait for high priority requests if this is a lower priority request
    await waitForHighPriorityRequests(priority);

    console.log(`🔄 Cache miss - generating new questions with AI (timeout: ${API_TIMEOUT_THRESHOLD}ms)...`);
    const questionSpecs = getMathQuestionSpecs(level);
    console.log(`📋 Question specs: ${questionSpecs.count} questions, max score: ${questionSpecs.maxScore}`);

    // Try to generate with AI using batching for large requests
    let questions;
    const initialRetryCount = performanceMetrics.retryAttempts;
    try {
      console.log(`🤖 Attempting mathematics AI generation for ${questionSpecs.count} questions...`);

      questions = await generateQuestionsInBatches(
        level,
        studentLevel,
        questionSpecs,
        createOptimizedMathQuestionPrompt,
        makeOpenAICallWithRetry,
        validateQuestions,
        priority
      );

      // Track successful retry if retries were made during this call
      const retriesMadeDuringCall = performanceMetrics.retryAttempts - initialRetryCount;
      if (retriesMadeDuringCall > 0) {
        performanceMetrics.successfulRetries++;
        console.log(`✅ Mathematics successful retry after ${retriesMadeDuringCall} attempts`);
      }

      if (!questions || questions.length === 0) {
        throw new Error('No valid questions generated from batches');
      }

      console.log(`✅ Generated ${questions.length} mathematics questions with AI (batched)`);
    } catch (apiError) {
      console.error(`Mathematics AI generation failed for ${level}:`, apiError.message);
      throw apiError;
    }

    // Ensure we have the right number of questions
    if (questions.length < questionSpecs.count) {
      console.warn(`Generated ${questions.length} questions, expected ${questionSpecs.count}. Using fallback for remaining.`);
      const fallbackQuestions = generateEnhancedFallbackMathQuestions(level);
      const additionalQuestions = fallbackQuestions.slice(questions.length);
      questions.push(...additionalQuestions.slice(0, questionSpecs.count - questions.length));
    }

    // Cache the questions in both in-memory and database
    setCachedQuestions(level, studentLevel, questions);

    // Store in database cache for long-term persistence (async, don't wait)
    setDatabaseCachedQuestions('math', level, studentLevel, questions, {
      generatedAt: new Date().toISOString(),
      priority: priority,
      requestId: requestId,
      generationTimeMs: Date.now() - startTime
    }).catch(error => {
      console.warn(`Failed to store math questions in database cache: ${error.message}`);
    });

    const generationTime = Date.now() - startTime;
    console.log(`Generated ${questions.length} mathematics questions for ${level} level in ${generationTime}ms (Priority: ${priority})`);
    updateAverageGenerationTime(generationTime);

    removeActiveRequest(requestId, priority);
    return questions;

  } catch (error) {
    console.error(`Error generating mathematics questions for ${level} (Priority: ${priority}):`, error);
    performanceMetrics.fallbackUsage++;
    const fallbackQuestions = generateEnhancedFallbackMathQuestions(level);
    const generationTime = Date.now() - startTime;
    updateAverageGenerationTime(generationTime);

    removeActiveRequest(requestId, priority);
    return fallbackQuestions;
  }
}

// ============================================================================
// FALLBACK QUESTION GENERATION
// ============================================================================

// Enhanced fallback question generation with better quality and coverage
function generateEnhancedFallbackMathQuestions(level) {
  // Check if we have cached fallback questions
  const fallbackCacheKey = `fallback_${level}`;
  const cachedFallback = mathQuestionCache.get(fallbackCacheKey);

  if (cachedFallback && Date.now() - cachedFallback.timestamp < CACHE_EXPIRY_TIME) {
    console.log(`Using cached fallback questions for ${level}`);
    return cachedFallback.questions;
  }

  // Get question specifications for the level
  const questionSpecs = getMathQuestionSpecs(level);

  console.log(`🔧 Generating enhanced fallback questions for ${level} level...`);

  // Use the improved fallback generation with deduplication
  const result = generateFallbackMathQuestions(level);

  // Cache the fallback questions for future use
  mathQuestionCache.set(fallbackCacheKey, {
    questions: result,
    timestamp: Date.now()
  });

  console.log(`✅ Generated and cached ${result.length} enhanced fallback questions for ${level}`);
  return result;
}

// Keep original fallback function for backward compatibility
function generateFallbackMathQuestions(level) {
  const fallbackQuestions = {
    'Entry': [
      // Arithmetic Questions (30% - 7 questions)
      {
        id: 1,
        type: "multiple-choice",
        topic: "arithmetic",
        question: "A recipe calls for 250g of flour, but you only have a 150g bag and a 75g bag. How much more flour do you need?",
        options: ["25g", "50g", "75g", "100g"],
        correctAnswer: "25g",
        points: 2,
        explanation: "You have 150g + 75g = 225g. You need 250g - 225g = 25g more."
      },
      {
        id: 2,
        type: "multiple-choice",
        topic: "arithmetic",
        question: "If you save £8 each week, how many weeks will it take to save £100?",
        options: ["11 weeks", "12 weeks", "13 weeks", "14 weeks"],
        correctAnswer: "13 weeks",
        points: 2,
        explanation: "£100 ÷ £8 = 12.5 weeks, so you need 13 complete weeks to have at least £100."
      },
      {
        id: 3,
        type: "multiple-choice",
        topic: "arithmetic",
        question: "Which calculation gives the largest result?",
        options: ["7 × 8", "9 × 6", "4 × 15", "12 × 5"],
        correctAnswer: "4 × 15",
        points: 2,
        explanation: "7×8=56, 9×6=54, 4×15=60, 12×5=60. Both 4×15 and 12×5 equal 60, which is largest."
      },
      {
        id: 4,
        type: "multiple-choice",
        topic: "arithmetic",
        question: "A parking meter costs £1.20 for 2 hours. How much would 5 hours cost?",
        options: ["£2.40", "£3.00", "£3.60", "£6.00"],
        correctAnswer: "£3.00",
        points: 2,
        explanation: "£1.20 for 2 hours = £0.60 per hour. 5 hours × £0.60 = £3.00"
      },
      {
        id: 5,
        type: "numeric",
        topic: "arithmetic",
        question: "You buy 3 coffees at £2.40 each and 2 sandwiches at £3.75 each. What is the total cost?",
        correctAnswer: "14.70",
        points: 2,
        explanation: "Coffees: 3 × £2.40 = £7.20. Sandwiches: 2 × £3.75 = £7.50. Total: £7.20 + £7.50 = £14.70"
      },
      {
        id: 6,
        type: "multiple-choice",
        topic: "arithmetic",
        question: "What number, when multiplied by 8, gives 72?",
        options: ["8", "9", "10", "11"],
        correctAnswer: "9",
        points: 2,
        explanation: "72 ÷ 8 = 9, so 9 × 8 = 72"
      },
      {
        id: 7,
        type: "multiple-choice",
        topic: "arithmetic",
        question: "Estimate the answer to 19 × 21 by rounding to the nearest 10.",
        options: ["380", "400", "420", "440"],
        correctAnswer: "400",
        points: 2,
        explanation: "Round 19 to 20 and 21 to 20. Then 20 × 20 = 400. (Actual answer is 399)"
      },
      // Fractions Questions (20% - 4 questions)
      {
        id: 8,
        type: "multiple-choice",
        topic: "fractions",
        question: "What is 1/2 + 1/4?",
        options: ["1/6", "2/6", "3/4", "1/3"],
        correctAnswer: "3/4",
        points: 2,
        explanation: "1/2 + 1/4 = 2/4 + 1/4 = 3/4"
      },
      {
        id: 9,
        type: "multiple-choice",
        topic: "fractions",
        question: "What is 3/4 of 20?",
        options: ["12", "15", "16", "18"],
        correctAnswer: "15",
        points: 2,
        explanation: "3/4 × 20 = (3 × 20) ÷ 4 = 60 ÷ 4 = 15"
      },
      {
        id: 10,
        type: "multiple-choice",
        topic: "fractions",
        question: "Which fraction is equivalent to 0.5?",
        options: ["1/3", "1/2", "2/3", "3/4"],
        correctAnswer: "1/2",
        points: 2,
        explanation: "0.5 = 5/10 = 1/2"
      },
      {
        id: 11,
        type: "multiple-choice",
        topic: "fractions",
        question: "What is 2/3 - 1/6?",
        options: ["1/2", "1/3", "1/6", "2/9"],
        correctAnswer: "1/2",
        points: 2,
        explanation: "2/3 - 1/6 = 4/6 - 1/6 = 3/6 = 1/2"
      },
      // Percentages Questions (15% - 3 questions)
      {
        id: 12,
        type: "multiple-choice",
        topic: "percentages",
        question: "What is 25% of 80?",
        options: ["15", "20", "25", "30"],
        correctAnswer: "20",
        points: 2,
        explanation: "25% of 80 = 25/100 × 80 = 0.25 × 80 = 20"
      },
      {
        id: 13,
        type: "multiple-choice",
        topic: "percentages",
        question: "What percentage is 15 out of 60?",
        options: ["20%", "25%", "30%", "35%"],
        correctAnswer: "25%",
        points: 2,
        explanation: "15/60 = 1/4 = 0.25 = 25%"
      },
      {
        id: 14,
        type: "numeric",
        topic: "percentages",
        question: "A shirt costs £40. If there's a 10% discount, what is the sale price?",
        correctAnswer: "36",
        points: 2,
        explanation: "10% of £40 = £4, so sale price = £40 - £4 = £36"
      }
    ],
    'Level1': [
      // Advanced Arithmetic Questions (25% - 3 questions)
      {
        id: 1,
        type: "multiple-choice",
        topic: "advancedArithmetic",
        question: "A temperature drops from 5°C to -8°C. By how many degrees has it fallen?",
        options: ["3°C", "8°C", "13°C", "15°C"],
        correctAnswer: "13°C",
        points: 2,
        explanation: "From 5°C to -8°C: 5 - (-8) = 5 + 8 = 13°C drop"
      },
      {
        id: 2,
        type: "multiple-choice",
        topic: "advancedArithmetic",
        question: "A builder needs 24 bricks per square metre. If a wall is 3m × 2.5m, how many bricks are needed?",
        options: ["180", "200", "220", "240"],
        correctAnswer: "180",
        points: 2,
        explanation: "Wall area: 3 × 2.5 = 7.5 m². Bricks needed: 7.5 × 24 = 180 bricks"
      },
      {
        id: 3,
        type: "multiple-choice",
        topic: "advancedArithmetic",
        question: "A shop offers 'Buy 2, get 1 free' on items costing £8 each. How much would 6 items cost?",
        options: ["£32", "£40", "£48", "£16"],
        correctAnswer: "£32",
        points: 2,
        explanation: "For every 3 items, you pay for 2. For 6 items: pay for 4 items. 4 × £8 = £32"
      }
    ]
  };

  const questions = fallbackQuestions[level] || fallbackQuestions['Entry'];
  const questionSpecs = getMathQuestionSpecs(level);

  // Ensure we have enough questions by repeating if necessary
  const result = [];
  for (let i = 0; i < questionSpecs.count; i++) {
    const questionIndex = i % questions.length;
    const baseQuestion = questions[questionIndex];
    result.push({
      ...baseQuestion,
      id: i + 1 // Ensure unique IDs
    });
  }

  return result.slice(0, questionSpecs.count);
}

// ============================================================================
// DIGITAL SKILLS ASSESSMENT FUNCTIONS
// ============================================================================

// Generate digital skills questions using simplified AI generation
async function generateDigitalSkillsQuestions(level, studentLevel) {
  console.log(`Generating digital skills questions for ${level} level...`);

  try {
    // Check database cache first
    const cacheKey = `digital_skills_${level}`;
    const cachedQuestions = await getDatabaseCachedQuestions('digital', level);

    if (cachedQuestions) {
      console.log(`Digital skills questions served from database cache for ${level}`);
      return cachedQuestions;
    }

    console.log(`Cache miss - generating new digital skills questions with AI...`);
    const questionSpecs = getDigitalSkillsQuestionSpecs(level);
    console.log(`Question specs: ${questionSpecs.count} questions, max score: ${questionSpecs.maxScore}`);

    // Create AI prompt
    const prompt = createDigitalSkillsQuestionPrompt(level, questionSpecs, studentLevel);

    // Make single API call to OpenAI
    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: `You are an expert digital skills assessment designer specializing in computer literacy and digital competency evaluation. Your role is to create high-quality, practical digital skills questions for adult learners and students.

CRITICAL REQUIREMENTS:
- Return ONLY valid JSON array format
- No additional text, explanations, or markdown
- Each question must have all required fields
- Questions should be practical and relevant to real-world digital skills
- Focus on computer literacy, internet skills, and digital applications`
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 4000,
      temperature: 0.7
    });

    // Parse AI response
    const content = completion.choices[0].message.content;
    const questions = parseAIQuestionsResponse(content, level);

    if (!questions || questions.length === 0) {
      throw new Error('Failed to parse AI response or no questions generated');
    }

    // Validate questions
    const validatedQuestions = validateDigitalSkillsQuestions(questions, level);

    if (validatedQuestions.length === 0) {
      throw new Error('No valid questions after validation');
    }

    console.log(`✅ Generated ${validatedQuestions.length} digital skills questions with AI`);

    // Store in database cache
    await setDatabaseCachedQuestions('digital', level, null, validatedQuestions, {
      generatedAt: new Date().toISOString(),
      cacheKey: cacheKey
    });

    return validatedQuestions;

  } catch (error) {
    console.error(`Error generating digital skills questions for ${level}:`, error);
    throw error; // Return error to client instead of using fallback
  }
}



// Create expertly designed prompt for high-quality digital skills question generation
function createDigitalSkillsQuestionPrompt(level, questionSpecs, studentLevel) {
  // Enhanced topic descriptions with learning objectives and real-world contexts
  const topicDescriptions = {
    'computerBasics': 'Hardware identification, system components, and fundamental computing concepts in workplace/home environments',
    'mouseKeyboard': 'Precise input device operation, keyboard shortcuts, and efficient navigation techniques for productivity',
    'basicOperations': 'System startup/shutdown procedures, basic file operations, and essential computer maintenance tasks',
    'fileManagement': 'Systematic file organization, folder structures, backup strategies, and file type understanding',
    'basicSafety': 'Password security fundamentals, privacy protection, and safe computing practices',
    'laptopDesktop': 'Device selection criteria, portability considerations, and hardware configuration differences',
    'applications': 'Software installation, application management, and basic program functionality',
    'internetSafety': 'Threat recognition, secure browsing practices, and digital risk management',
    'emailBasics': 'Professional email communication, attachment handling, and email security protocols',
    'digitalCitizenship': 'Online ethics, digital footprint management, and responsible technology use',
    'microsoftApps': 'Productivity suite proficiency for document creation, data analysis, and presentation design',
    'onlineBanking': 'Secure financial transactions, fraud prevention, and digital banking best practices',
    'cloudStorage': 'Cloud service utilization, file synchronization, and collaborative storage management',
    'digitalIdentity': 'Online profile management, privacy settings, and digital reputation control',
    'internetSkills': 'Advanced search techniques, source evaluation, and information literacy',
    'advancedFormatting': 'Professional document design, style management, and layout optimization',
    'spreadsheets': 'Data analysis, formula creation, and spreadsheet-based problem solving',
    'presentations': 'Visual communication design, audience engagement, and presentation technology',
    'workplaceSkills': 'Professional digital communication, collaboration tools, and workplace technology integration',
    'collaboration': 'Team-based digital tools, shared workspace management, and remote collaboration',
    'operatingSystems': 'System administration basics, software management, and OS optimization',
    'emailProficiency': 'Advanced email organization, automation, and professional communication standards',
    'onlineTransactions': 'E-commerce security, payment protection, and transaction verification',
    'digitalSafety': 'Advanced threat mitigation, security software, and incident response',
    'troubleshooting': 'Systematic problem diagnosis, solution implementation, and technical support',
    'timedExam': 'Exam strategy, time management, and assessment performance optimization',
    'wordAdvanced': 'Advanced document automation, collaboration features, and professional publishing',
    'excelAdvanced': 'Complex data analysis, advanced formulas, and business intelligence applications',
    'powerpointAdvanced': 'Interactive presentations, multimedia integration, and advanced design techniques',
    'employmentSkills': 'Job-relevant digital competencies, professional online presence, and career development',
    'advancedMicrosoft': 'Enterprise-level application features, integration, and advanced productivity techniques',
    'itCareers': 'Technical career preparation, industry standards, and professional development',
    'higherEducation': 'Academic technology use, research tools, and educational platform proficiency',
    'professionalSkills': 'Industry-standard digital competencies and professional certification preparation',
    'certification': 'Exam preparation strategies, industry standards, and certification requirements'
  };

  // Define cognitive complexity levels for each assessment level
  const cognitiveComplexity = {
    'EntryLevel2': 'Basic recall and recognition - identify, name, define, list basic concepts',
    'EntryLevel2Plus': 'Basic comprehension and simple application - explain, demonstrate, use basic functions',
    'Level1': 'Application and analysis - apply procedures, solve problems, compare options',
    'Level2': 'Analysis and evaluation - analyze situations, evaluate solutions, make informed decisions',
    'EntryLevel3': 'Application with some analysis - implement procedures, troubleshoot basic issues',
    'ICDLLevel2': 'Advanced application and synthesis - integrate multiple skills, create solutions',
    'ICDLLevel3': 'Evaluation and synthesis - assess complex scenarios, design comprehensive solutions',
    'Level3': 'Expert-level evaluation and creation - optimize workflows, mentor others, innovate solutions'
  };

  // Create topic distribution based on level
  const topicDistribution = questionSpecs.topics.map(topic => {
    return {
      topic,
      description: topicDescriptions[topic] || topic,
      count: Math.max(1, Math.floor(questionSpecs.count / questionSpecs.topics.length))
    };
  });

  // Enhanced difficulty adjustment based on student demographics
  const difficultyAdjustment = studentLevel === 'adult-learner' ?
    'Frame scenarios in adult workplace and personal contexts. Use terminology familiar to working adults. Focus on practical applications relevant to career advancement and daily life management.' :
    'Use educational and career preparation contexts. Include scenarios relevant to academic success and future employment. Balance theoretical understanding with practical application.';

  return `
  You are an expert digital skills assessment designer specializing in creating professional-grade questions equivalent to ICDL/ECDL and industry certification standards. Create ${questionSpecs.count} expertly designed digital skills assessment questions for ${level} level.

  COGNITIVE COMPLEXITY TARGET: ${cognitiveComplexity[level] || cognitiveComplexity['Level1']}

  QUESTION DISTRIBUTION:
  ${topicDistribution.map(t => `- ${t.count} questions on ${t.topic}: ${t.description}`).join('\n')}

  ASSESSMENT DESIGN PRINCIPLES:

  1. REALISTIC SCENARIOS: Frame all questions within authentic workplace, educational, or personal contexts that learners actually encounter
  2. CLEAR LEARNING OBJECTIVES: Each question must test specific, measurable digital competencies aligned with industry standards
  3. APPROPRIATE DIFFICULTY: Match cognitive complexity to level expectations (recall → comprehension → application → analysis → evaluation)
  4. PROFESSIONAL LANGUAGE: Use terminology consistent with industry standards and certification exams

  QUESTION FORMAT REQUIREMENTS:

  1. MULTIPLE-CHOICE QUESTIONS (50% of questions):
     - Present realistic scenarios with 4 sophisticated answer choices
     - Each question must have exactly one correct answer
     - Worth 2 points each
     - Focus on practical application within real-world contexts

     ANSWER CHOICE SOPHISTICATION REQUIREMENTS:
     ✓ PLAUSIBLE DISTRACTORS: Incorrect options must represent common misconceptions or partial understanding
     ✓ AVOID OBVIOUS ELIMINATION: No obviously wrong answers like "Delete everything" or "Buy a new computer"
     ✓ SIMILAR COMPLEXITY: All options should be of comparable length and technical sophistication
     ✓ COMMON ERRORS: Include distractors that represent typical beginner mistakes
     ✓ AVOID ABSOLUTE TERMS: Minimize "always," "never," "all," "none" which signal correct/incorrect answers
     ✓ BALANCED OPTIONS: Prevent predictable patterns (longest answer isn't always correct)

  2. PRACTICAL COMPETENCY ASSESSMENT QUESTIONS (50% of questions):
     - Use type: "self-assessment-practical"
     - Focus on specific competency levels and actual behaviors
     - Worth 2 points each
     - Create task-specific options reflecting meaningful skill gradations
     - 4-5 options progressing from expert to novice competency
     - Options describe actual behaviors, knowledge, or implementation levels

  EXAMPLES OF HIGH-QUALITY QUESTIONS:

  EXCELLENT MULTIPLE-CHOICE EXAMPLE (Level1):
  "You're working on an important presentation for tomorrow's meeting when your computer suddenly freezes. The presentation hasn't been saved in the last 30 minutes. What is the most appropriate first step to take?"
  A) "Force restart the computer immediately to resolve the freezing issue"
  B) "Wait 2-3 minutes to see if the system recovers, then use Ctrl+Alt+Del if needed"
  C) "Unplug the computer to ensure a complete restart and prevent data corruption"
  D) "Try clicking rapidly on different areas of the screen to regain control"
  [Correct: B - demonstrates proper troubleshooting sequence and system recovery procedures]

  POOR MULTIPLE-CHOICE EXAMPLE (avoid this):
  "What should you do when your computer freezes?"
  A) "Restart it"
  B) "Buy a new computer"
  C) "Throw it away"
  D) "Call technical support immediately"
  [Problems: Lacks context, obvious wrong answers, no real-world scenario]

  EXCELLENT PRACTICAL COMPETENCY EXAMPLE:
  "When collaborating on documents with colleagues, how do you typically manage version control and track changes?"
  * "I use built-in collaboration features (Track Changes, Comments, Version History) and establish clear naming conventions with team members"
  * "I save multiple versions with different names and manually compare changes, sometimes using basic commenting features"
  * "I email documents back and forth and try to remember what changes were made, occasionally losing track of the latest version"
  * "I'm not familiar with collaboration features and usually work on documents alone or print them for review"
  * "I haven't collaborated on digital documents before"

  SCENARIO-BASED QUESTION EXAMPLES BY LEVEL:

  EntryLevel2 - Basic Recognition:
  "You're helping a friend set up their first email account. Which of these is the most important security consideration during the setup process?"

  Level1 - Application:
  "Your supervisor asks you to create a budget spreadsheet that automatically calculates totals. Which approach would be most efficient?"

  Level2 - Analysis:
  "You notice your company's shared cloud folder is becoming disorganized, affecting team productivity. What systematic approach would best address this issue?"

  ICDLLevel3 - Evaluation:
  "Your organization is considering migrating from desktop software to cloud-based productivity tools. What factors should be evaluated to make an informed recommendation?"

  STUDENT CONTEXT: ${difficultyAdjustment}

  CRITICAL QUALITY STANDARDS:

  MULTIPLE-CHOICE QUESTION REQUIREMENTS:
  ✓ Present authentic workplace/educational scenarios
  ✓ Test specific competencies, not general knowledge
  ✓ Create sophisticated distractors based on common misconceptions
  ✓ Ensure all options are plausible and of similar complexity
  ✓ Avoid absolute terms and predictable patterns
  ✓ Use professional terminology appropriate for the level
  ✓ Frame questions as problem-solving situations

  PRACTICAL COMPETENCY QUESTION REQUIREMENTS:
  ✓ Focus on actual behaviors and implementation approaches
  ✓ Avoid confidence-based language ("I feel comfortable")
  ✓ Include specific technical details that demonstrate skill level
  ✓ Progress from expert best practices to novice/no experience
  ✓ Make options mutually exclusive and clearly differentiated
  ✓ Describe what people DO, not how they FEEL
  ✓ Ensure highest option represents industry best practices

  DISTRACTOR DESIGN PRINCIPLES:
  - Base incorrect options on actual student errors and misconceptions
  - Include partially correct approaches that demonstrate incomplete understanding
  - Use realistic but suboptimal solutions that beginners might choose
  - Avoid obviously wrong answers that can be eliminated immediately
  - Ensure distractors are believable to someone with limited knowledge

  RESPONSE FORMAT:
  Return ONLY a valid JSON array with no additional text, explanations, or formatting:

  [
    {
      "id": 1,
      "type": "multiple-choice",
      "question": "Realistic scenario-based question text",
      "options": ["Sophisticated option A", "Plausible option B", "Believable option C", "Reasonable option D"],
      "correctAnswer": "Sophisticated option A",
      "points": 2,
      "topic": "topicName"
    },
    {
      "id": 2,
      "type": "self-assessment-practical",
      "question": "How do you typically approach [specific task/skill]?",
      "options": [
        "Expert-level behavior with specific technical details",
        "Competent approach with minor limitations or gaps",
        "Basic implementation with significant limitations",
        "Minimal understanding with frequent assistance needed",
        "No experience or knowledge in this area"
      ],
      "correctAnswer": "Expert-level behavior with specific technical details",
      "points": 2,
      "topic": "topicName"
    }
  ]

  FINAL REQUIREMENTS:
  - Generate exactly ${questionSpecs.count} questions
  - Maintain 50/50 split between multiple-choice and practical competency questions
  - Ensure each question tests a specific, measurable competency
  - Use realistic scenarios that learners would actually encounter
  - Create sophisticated answer choices that avoid obvious elimination
  - Return ONLY valid JSON with no additional text or explanations
  `;
}

// Validate digital skills questions
function validateDigitalSkillsQuestions(questions, level) {
  const questionSpecs = getDigitalSkillsQuestionSpecs(level);
  const validQuestions = [];

  for (const question of questions) {
    // Basic validation
    if (!question.question || !question.correctAnswer) {
      console.warn('Skipping invalid question:', question);
      continue;
    }

    // Determine question type
    const questionType = question.type || 'multiple-choice';

    // Validate based on question type
    if (questionType === 'multiple-choice') {
      // Ensure options is an array with 4 items for multiple-choice
      if (!question.options || !Array.isArray(question.options) || question.options.length !== 4) {
        console.warn('Question has invalid options:', question);
        continue;
      }

      // Ensure correctAnswer is one of the options
      if (!question.options.includes(question.correctAnswer)) {
        console.warn('Question has invalid correctAnswer:', question);
        continue;
      }

      // Normalize multiple-choice question structure
      validQuestions.push({
        id: question.id || validQuestions.length + 1,
        question: question.question,
        options: question.options,
        correctAnswer: question.correctAnswer,
        points: question.points || 2,
        topic: question.topic || questionSpecs.topics[0],
        type: 'multiple-choice'
      });

    } else if (questionType === 'self-assessment-practical') {
      // Validate practical competency assessment questions
      if (!question.options || !Array.isArray(question.options) || question.options.length < 4) {
        console.warn('Skipping practical assessment question - needs at least 4 competency-based options:', question);
        continue;
      }

      // Ensure correctAnswer is one of the options
      if (!question.options.includes(question.correctAnswer)) {
        // Default to first option (highest competency) if correctAnswer is invalid
        question.correctAnswer = question.options[0];
        console.warn('Corrected invalid correctAnswer for practical assessment question:', question.question);
      }

      // Normalize practical assessment question structure
      validQuestions.push({
        id: question.id || validQuestions.length + 1,
        question: question.question,
        options: question.options,
        correctAnswer: question.correctAnswer,
        points: question.points || 2,
        topic: question.topic || questionSpecs.topics[0],
        type: 'self-assessment-practical'
      });

    } else if (questionType === 'self-assessment-descriptive') {
      // Validate descriptive self-assessment questions
      if (!question.options || !Array.isArray(question.options) || question.options.length < 3) {
        console.warn('Skipping descriptive self-assessment question - invalid options:', question);
        continue;
      }

      // Ensure correctAnswer is one of the options
      if (!question.options.includes(question.correctAnswer)) {
        console.warn('Skipping descriptive self-assessment question - correct answer not in options:', question);
        continue;
      }

      // Normalize descriptive self-assessment question structure
      validQuestions.push({
        id: question.id || validQuestions.length + 1,
        question: question.question,
        options: question.options,
        correctAnswer: question.correctAnswer,
        points: question.points || 2,
        topic: question.topic || questionSpecs.topics[0],
        type: 'self-assessment-descriptive'
      });

    } else if (questionType.startsWith('self-assessment')) {
      // Validate other self-assessment questions (stars, slider, rating)
      const validTypes = ['self-assessment-stars', 'self-assessment-slider', 'self-assessment-rating'];
      if (!validTypes.includes(questionType)) {
        console.warn('Skipping question - invalid self-assessment type:', question);
        continue;
      }

      // Normalize self-assessment question structure
      validQuestions.push({
        id: question.id || validQuestions.length + 1,
        question: question.question,
        correctAnswer: question.correctAnswer || '5', // Default to middle rating
        points: question.points || 2,
        topic: question.topic || questionSpecs.topics[0],
        type: questionType
      });

    } else {
      console.warn('Skipping question - unknown question type:', question);
      continue;
    }
  }

  console.log(`Validated ${validQuestions.length}/${questions.length} digital skills questions for ${level}`);
  return validQuestions;
}




// ============================================================================
// ASSESSMENT ANALYSIS FUNCTIONS
// ============================================================================

// Create AI prompt for assessment analysis
function createMathAnalysisPrompt(answers, level, timeSpent, questions) {
  const questionSpecs = getMathQuestionSpecs(level);

  // Create a map of question IDs to questions for easy lookup
  const questionMap = {};
  if (questions && Array.isArray(questions)) {
    questions.forEach(q => {
      questionMap[q.id] = q;
    });
  }

  return `Analyze this mathematics assessment for ${level} level:

ASSESSMENT DETAILS:
- Level: ${level}
- Questions: ${questionSpecs.count}
- Maximum Score: ${questionSpecs.maxScore}
- Passing Score: ${questionSpecs.passingScore}
- Time Spent: ${timeSpent || 'unknown'} seconds
- Answers Provided: ${answers.length}

STUDENT RESPONSES:
${answers.map((answer, index) => {
  const question = questionMap[answer.questionId];
  const questionText = question ? question.question : 'Question not found';
  const correctAnswer = question ? question.correctAnswer : 'Unknown';
  const points = question ? question.points : 2;

  return `Question ${index + 1}: "${questionText}" - Student Answer: "${answer.studentAnswer || 'No answer'}" - Correct Answer: "${correctAnswer}" - Topic: ${answer.topic || 'unknown'} - Points: ${points}`;
}).join('\n')}

SCORING INSTRUCTIONS:
1. Compare each student answer with the correct answer
2. Award full points for exact matches (case-insensitive for text answers)
3. Award partial credit for close numerical answers (within reasonable rounding)
4. Sum up all points earned to get the total score
5. Determine pass/fail based on whether score >= passing score (${questionSpecs.passingScore})

Please provide a comprehensive analysis in JSON format with:
{
  "score": <total_points_earned_as_number>,
  "passed": <true_or_false>,
  "topicBreakdown": {
    "arithmetic": {"correct": 0, "total": 0, "percentage": 0},
    "fractions": {"correct": 0, "total": 0, "percentage": 0},
    "percentages": {"correct": 0, "total": 0, "percentage": 0},
    "algebra": {"correct": 0, "total": 0, "percentage": 0},
    "geometry": {"correct": 0, "total": 0, "percentage": 0},
    "statistics": {"correct": 0, "total": 0, "percentage": 0}
  },
  "feedback": {
    "numericalSkills": "<detailed_feedback>",
    "algebraicThinking": "<detailed_feedback>",
    "problemSolving": "<detailed_feedback>",
    "geometricReasoning": "<detailed_feedback>",
    "dataHandling": "<detailed_feedback>",
    "overall": "<overall_assessment>"
  },
  "strengths": ["<strength_1>", "<strength_2>"],
  "improvements": ["<improvement_1>", "<improvement_2>"],
  "placementRecommendation": {
    "level": "<recommended_level>",
    "reasoning": "<explanation>",
    "nextSteps": ["<step_1>", "<step_2>"],
    "courseRecommendations": ["<course_1>", "<course_2>"]
  }
}

CRITICAL REQUIREMENTS:
- Return ONLY valid JSON, no additional text or markdown
- Score must be a number, not a string
- Ensure all feedback uses UK mathematical terminology (e.g., 'maths' not 'math', 'brackets' not 'parentheses', 'anticlockwise' not 'counterclockwise')
- Base recommendations on actual demonstrated competency, not just completion
- Calculate score by carefully comparing student answers to correct answers`;
}

// Generate fallback analysis when AI analysis fails
function generateFallbackMathAnalysis(answers, level, questions) {
  const questionSpecs = getMathQuestionSpecs(level);

  console.log('DEBUG - Generating fallback analysis with:', {
    answerCount: answers.length,
    level,
    hasQuestions: !!questions,
    questionCount: questions ? questions.length : 0
  });

  // Create a map of question IDs to questions for easy lookup
  const questionMap = {};
  if (questions && Array.isArray(questions)) {
    questions.forEach(q => {
      questionMap[q.id] = q;
    });
  }

  // Calculate score based on correct answers
  let score = 0;
  let correctCount = 0;

  answers.forEach((answer, index) => {
    const question = questionMap[answer.questionId];
    if (question && answer.studentAnswer) {
      const studentAns = String(answer.studentAnswer).trim().toLowerCase();
      const correctAns = String(question.correctAnswer).trim().toLowerCase();
      const points = question.points || 2;

      console.log(`DEBUG - Question ${index + 1}: Student="${studentAns}" vs Correct="${correctAns}" Points=${points}`);

      if (studentAns === correctAns) {
        score += points;
        correctCount++;
        console.log(`DEBUG - Correct! Added ${points} points. Total: ${score}`);
      }
    } else {
      console.log(`DEBUG - Question ${index + 1}: Missing question data or student answer`);
    }
  });

  const passed = score >= questionSpecs.passingScore;

  console.log('DEBUG - Fallback analysis result:', {
    score,
    correctCount,
    totalQuestions: answers.length,
    passingScore: questionSpecs.passingScore,
    passed
  });

  return {
    score: score,
    passed: passed,
    topicBreakdown: {},
    feedback: {
      numericalSkills: `Answered ${correctCount} out of ${answers.length} questions correctly`,
      algebraicThinking: 'Algebraic skills evaluated',
      problemSolving: 'Problem-solving assessed',
      geometricReasoning: 'Geometric understanding reviewed',
      dataHandling: 'Data handling skills assessed',
      overall: `Mathematics assessment completed with ${score} points out of ${questionSpecs.maxScore}`
    },
    strengths: correctCount > 0 ? [`Correctly answered ${correctCount} questions`] : ['Completed the mathematics assessment'],
    improvements: score < questionSpecs.passingScore ? ['Continue practicing mathematics to improve score'] : ['Continue building on current skills'],
    placementRecommendation: {
      level: passed ? level : 'Entry Support',
      reasoning: `Based on score of ${score}/${questionSpecs.maxScore} points`,
      nextSteps: passed ? ['Continue to next level'] : ['Review fundamental concepts'],
      courseRecommendations: passed ? ['Advanced mathematics course'] : ['Basic mathematics course']
    }
  };
}

// Analyze mathematics assessment using AI
async function analyzeMathematicsAssessment(answers, level, timeSpent, questions) {
  try {
    console.log('Analyzing mathematics assessment with AI...');
    console.log('DEBUG - Answers received for analysis:', {
      answerCount: answers.length,
      sampleAnswers: answers.slice(0, 3),
      level,
      timeSpent,
      questionsProvided: !!questions,
      questionCount: questions ? questions.length : 0
    });

    const analysisPrompt = createMathAnalysisPrompt(answers, level, timeSpent, questions);
    console.log('DEBUG - Analysis prompt created, length:', analysisPrompt.length);

    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "You are an expert mathematics assessor specialising in evaluating mathematical proficiency for UK educational placement. Provide detailed analysis using UK mathematical terminology and educational standards."
        },
        {
          role: "user",
          content: analysisPrompt
        }
      ],
      max_tokens: 1500,
      temperature: 0.3
    });

    const content = completion.choices[0].message.content;
    console.log('AI analysis response received');
    console.log('DEBUG - AI response content:', content.substring(0, 500) + '...');

    let analysisResult;
    try {
      // Try direct JSON parse first
      analysisResult = JSON.parse(content);
      console.log('DEBUG - Direct JSON parse successful');
    } catch (parseError) {
      console.log('DEBUG - Direct JSON parse failed, trying extraction...');

      // Try to extract JSON from markdown or mixed content
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          analysisResult = JSON.parse(jsonMatch[0]);
          console.log('DEBUG - JSON extraction successful');
        } catch (extractError) {
          console.log('DEBUG - JSON extraction failed, trying cleanup...');

          // Try cleaning the content
          const cleanedContent = content
            .replace(/```json/g, '')
            .replace(/```/g, '')
            .replace(/^\s*[\w\s]*:\s*/, '')
            .trim();

          try {
            analysisResult = JSON.parse(cleanedContent);
            console.log('DEBUG - Cleaned JSON parse successful');
          } catch (cleanError) {
            console.warn('All JSON parsing strategies failed, using fallback');
            console.log('DEBUG - Parse error:', parseError.message);
            console.log('DEBUG - Raw content that failed to parse:', content.substring(0, 1000));
            return generateFallbackMathAnalysis(answers, level, questions);
          }
        }
      } else {
        console.warn('No JSON found in response, using fallback');
        console.log('DEBUG - Raw content:', content.substring(0, 1000));
        return generateFallbackMathAnalysis(answers, level, questions);
      }
    }

    console.log('DEBUG - Parsed analysis result:', {
      score: analysisResult.score,
      scoreType: typeof analysisResult.score,
      passed: analysisResult.passed,
      hasTopicBreakdown: !!analysisResult.topicBreakdown,
      hasFeedback: !!analysisResult.feedback
    });

    // Validate and normalize the results
    const maxScore = getMathMaxScore(level);
    const passingScore = getMathPassingScore(level);

    analysisResult.score = Math.max(0, Math.min(maxScore, parseInt(analysisResult.score) || 0));
    analysisResult.passed = analysisResult.score >= passingScore;
    analysisResult.level = level;
    analysisResult.maxScore = maxScore;
    analysisResult.passingScore = passingScore;

    console.log('Mathematics assessment analysis completed:', {
      level,
      score: analysisResult.score,
      maxScore,
      passed: analysisResult.passed
    });

    return analysisResult;

  } catch (error) {
    console.error('Error in AI mathematics assessment analysis:', error);
    return generateFallbackMathAnalysis(answers, level, questions);
  }
}

// ============================================================================
// DIGITAL SKILLS AI ANALYSIS FUNCTIONS
// ============================================================================

// Create AI analysis prompt for digital skills assessment
function createDigitalSkillsAnalysisPrompt(answers, level, timeSpent, questions) {
  const questionSpecs = getDigitalSkillsQuestionSpecs(level);

  // Categorize answers by question type
  const multipleChoiceAnswers = [];
  const interactiveRatingAnswers = [];
  const descriptiveConfidenceAnswers = [];

  answers.forEach((answer, index) => {
    const question = questions[index];
    if (!question) return;

    const answerData = {
      question: question.question,
      userAnswer: answer.answer,
      correctAnswer: question.correctAnswer,
      topic: question.topic,
      type: question.type
    };

    if (question.type === 'multiple-choice') {
      answerData.options = question.options;
      answerData.isCorrect = answer.answer === question.correctAnswer;
      multipleChoiceAnswers.push(answerData);
    } else if (question.type && question.type.startsWith('self-assessment') && question.type !== 'self-assessment-descriptive') {
      answerData.rating = answer.answer;
      interactiveRatingAnswers.push(answerData);
    } else if (question.type === 'self-assessment-descriptive') {
      answerData.confidenceLevel = answer.answer;
      answerData.options = question.options;
      descriptiveConfidenceAnswers.push(answerData);
    }
  });

  return `
DIGITAL SKILLS ASSESSMENT ANALYSIS REQUEST

ASSESSMENT CONTEXT:
- Level: ${level}
- Total Questions: ${questions.length}
- Time Spent: ${Math.round(timeSpent / 60)} minutes
- Maximum Score: ${questionSpecs.maxScore}
- Passing Score: ${questionSpecs.passingScore}

QUESTION TYPE BREAKDOWN:
- Multiple-Choice Knowledge Questions: ${multipleChoiceAnswers.length}
- Interactive Self-Assessment Ratings: ${interactiveRatingAnswers.length}
- Descriptive Confidence Assessments: ${descriptiveConfidenceAnswers.length}

MULTIPLE-CHOICE RESPONSES:
${multipleChoiceAnswers.map((answer, index) => `
${index + 1}. Topic: ${answer.topic}
   Question: ${answer.question}
   User Answer: ${answer.userAnswer}
   Correct Answer: ${answer.correctAnswer}
   Result: ${answer.isCorrect ? 'CORRECT' : 'INCORRECT'}
`).join('')}

INTERACTIVE RATING RESPONSES:
${interactiveRatingAnswers.map((answer, index) => `
${index + 1}. Topic: ${answer.topic}
   Question: ${answer.question}
   User Rating: ${answer.rating}/5 or ${answer.rating}/10
   Assessment Type: ${answer.type}
`).join('')}

DESCRIPTIVE CONFIDENCE RESPONSES:
${descriptiveConfidenceAnswers.map((answer, index) => `
${index + 1}. Topic: ${answer.topic}
   Question: ${answer.question}
   User Response: "${answer.confidenceLevel}"
   Available Options: ${answer.options ? answer.options.join(' | ') : 'N/A'}
`).join('')}

ANALYSIS REQUIREMENTS:

Please provide a comprehensive analysis in the following JSON format:

{
  "score": [calculated total score based on correct answers and confidence levels],
  "skillsLevel": "[recommended course level from: Entry Level 2, Entry Level 2/3, Entry Level 3, Level 1, Level 2, Level 3]",
  "topicBreakdown": {
    "[topic1]": {"correct": [number], "total": [number], "percentage": [0-100]},
    "[topic2]": {"correct": [number], "total": [number], "percentage": [0-100]}
  },
  "feedback": {
    "basicComputerSkills": "[assessment of fundamental computer operation abilities]",
    "internetAndEmail": "[evaluation of online communication and browsing skills]",
    "digitalSafety": "[analysis of privacy, security, and safe online practices]",
    "softwareApplications": "[assessment of productivity software usage]",
    "troubleshooting": "[evaluation of problem-solving and technical support abilities]",
    "overall": "[comprehensive summary and skill level determination]"
  },
  "strengths": ["[specific strength 1]", "[specific strength 2]", "[specific strength 3]"],
  "improvements": ["[improvement area 1]", "[improvement area 2]", "[improvement area 3]"],
  "courseRecommendation": {
    "level": "[Entry Level 2|Entry Level 2/3|Entry Level 3|Level 1|Level 2|Level 3]",
    "title": "[specific course title]",
    "reasoning": "[explanation for recommendation based on assessment results]",
    "nextSteps": ["[actionable step 1]", "[actionable step 2]", "[actionable step 3]"]
  },
  "confidenceAnalysis": {
    "overallConfidence": "[High|Medium|Low]",
    "confidenceAreas": ["[area where user shows high confidence]"],
    "developmentAreas": ["[area where user needs confidence building]"],
    "practicalReadiness": "[assessment of readiness for real-world application]"
  }
}

SCORING GUIDELINES:
- Multiple-choice questions: 2 points for correct answers, 0 for incorrect
- Interactive ratings: Award points based on confidence level (higher ratings = more points)
- Descriptive confidence: Award points based on confidence level expressed

COURSE LEVEL RECOMMENDATIONS:
- Entry Level 2 (Computer Skills Beginners): For absolute beginners with minimal computer experience
- Entry Level 2/3 (Computer Skills Beginners Plus): For low-confidence users ready for some independence
- Entry Level 3 (Improvers Plus): For post-beginners with broader digital skills needs
- Level 1 (Computer Skills for Everyday Life): For basic users seeking practical application confidence
- Level 2 (Computer Skills for Work/ICDL Level 2): For job-focused office software skills
- Level 3 (ICDL Level 3): For advanced qualification seekers pursuing IT careers

Consider both objective knowledge scores AND subjective confidence levels when making recommendations.
Focus on practical digital skills scenarios and real-world application readiness.
`;
}

// Analyze digital skills assessment using AI
async function analyzeDigitalSkillsAssessment(answers, level, timeSpent, questions) {
  try {
    console.log('Analyzing digital skills assessment with AI...');
    console.log('DEBUG - Digital skills answers received for analysis:', {
      answerCount: answers.length,
      sampleAnswers: answers.slice(0, 3),
      level,
      timeSpent,
      questionsProvided: !!questions,
      questionCount: questions ? questions.length : 0
    });

    const analysisPrompt = createDigitalSkillsAnalysisPrompt(answers, level, timeSpent, questions);
    console.log('DEBUG - Digital skills analysis prompt created, length:', analysisPrompt.length);

    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "You are an expert digital skills assessor specialising in evaluating digital literacy and computer proficiency for UK educational placement. Provide detailed analysis using UK digital skills terminology and educational standards. Focus on practical application readiness and confidence levels."
        },
        {
          role: "user",
          content: analysisPrompt
        }
      ],
      max_tokens: 2000,
      temperature: 0.3
    });

    const content = completion.choices[0].message.content;
    console.log('AI digital skills analysis response received');
    console.log('DEBUG - AI response content:', content.substring(0, 500) + '...');

    // Parse the JSON response
    let analysisResult;
    try {
      // Extract JSON from the response (handle potential markdown formatting)
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        analysisResult = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No JSON found in response');
      }
    } catch (parseError) {
      console.error('Error parsing AI response JSON:', parseError);
      console.log('Raw AI response:', content);
      return generateFallbackDigitalSkillsAnalysis(answers, level, questions);
    }

    // Validate and normalize the results
    const questionSpecs = getDigitalSkillsQuestionSpecs(level);
    const maxScore = questionSpecs.maxScore;
    const passingScore = questionSpecs.passingScore;

    analysisResult.score = Math.max(0, Math.min(maxScore, parseInt(analysisResult.score) || 0));
    analysisResult.passed = analysisResult.score >= passingScore;
    analysisResult.level = level;
    analysisResult.maxScore = maxScore;
    analysisResult.passingScore = passingScore;

    // Ensure required fields exist with defaults
    analysisResult.feedback = analysisResult.feedback || {};
    analysisResult.strengths = analysisResult.strengths || ['Completed the assessment'];
    analysisResult.improvements = analysisResult.improvements || ['Continue practicing digital skills'];
    analysisResult.topicBreakdown = analysisResult.topicBreakdown || {};
    analysisResult.courseRecommendation = analysisResult.courseRecommendation || {
      level: level,
      title: 'Continue current level',
      reasoning: 'Based on assessment performance',
      nextSteps: ['Practice regularly', 'Focus on improvement areas']
    };
    analysisResult.confidenceAnalysis = analysisResult.confidenceAnalysis || {
      overallConfidence: 'Medium',
      confidenceAreas: ['Basic computer skills'],
      developmentAreas: ['Advanced applications'],
      practicalReadiness: 'Developing'
    };

    console.log('Digital skills assessment analysis completed:', {
      level,
      score: analysisResult.score,
      maxScore,
      passed: analysisResult.passed,
      recommendedLevel: analysisResult.courseRecommendation?.level
    });

    return analysisResult;

  } catch (error) {
    console.error('Error in AI digital skills assessment analysis:', error);
    return generateFallbackDigitalSkillsAnalysis(answers, level, questions);
  }
}

// Generate fallback analysis for digital skills assessment
function generateFallbackDigitalSkillsAnalysis(answers, level, questions) {
  console.log('Generating fallback digital skills analysis...');

  const questionSpecs = getDigitalSkillsQuestionSpecs(level);
  let score = 0;
  const topicBreakdown = {};

  // Calculate basic score
  answers.forEach((answer, index) => {
    const question = questions[index];
    if (!question) return;

    if (answer.answer && answer.answer.trim() !== '') {
      if (question.type === 'multiple-choice') {
        // Award points for correct answers only
        if (answer.answer === question.correctAnswer) {
          score += 2;
        }
      } else {
        // Award full points for self-assessment responses
        score += 2;
      }

      // Track topic performance
      const topic = question.topic || 'general';
      if (!topicBreakdown[topic]) {
        topicBreakdown[topic] = { correct: 0, total: 0, percentage: 0 };
      }
      topicBreakdown[topic].total += 1;
      if (question.type !== 'multiple-choice' || answer.answer === question.correctAnswer) {
        topicBreakdown[topic].correct += 1;
      }
    }
  });

  // Calculate percentages
  Object.keys(topicBreakdown).forEach(topic => {
    const data = topicBreakdown[topic];
    data.percentage = data.total > 0 ? Math.round((data.correct / data.total) * 100) : 0;
  });

  const passed = score >= questionSpecs.passingScore;

  return {
    score,
    passed,
    maxScore: questionSpecs.maxScore,
    passingScore: questionSpecs.passingScore,
    level,
    skillsLevel: level,
    topicBreakdown,
    feedback: {
      basicComputerSkills: 'Assessment completed - review performance by topic',
      internetAndEmail: 'Basic understanding demonstrated',
      digitalSafety: 'Awareness of digital safety concepts shown',
      softwareApplications: 'Familiarity with common applications',
      troubleshooting: 'Problem-solving approach developing',
      overall: `You scored ${score} out of ${questionSpecs.maxScore} points. ${passed ? 'Well done!' : 'Keep practicing to improve your skills.'}`
    },
    strengths: passed ?
      ['Good overall performance', 'Completed all questions', 'Shows digital literacy awareness'] :
      ['Completed the assessment', 'Shows willingness to learn', 'Basic understanding demonstrated'],
    improvements: passed ?
      ['Continue building advanced skills', 'Practice with real-world scenarios', 'Explore new digital tools'] :
      ['Review fundamental concepts', 'Practice basic computer operations', 'Build confidence with guided practice'],
    courseRecommendation: {
      level: passed ? getNextDigitalSkillsLevel(level) : level,
      title: passed ? 'Ready for next level' : 'Continue current level',
      reasoning: passed ? 'Strong performance indicates readiness for advancement' : 'Additional practice needed at current level',
      nextSteps: passed ?
        ['Enroll in next level course', 'Practice advanced skills', 'Apply skills in real situations'] :
        ['Review course materials', 'Practice fundamental skills', 'Seek additional support if needed']
    },
    confidenceAnalysis: {
      overallConfidence: passed ? 'Medium' : 'Low',
      confidenceAreas: ['Basic computer operations'],
      developmentAreas: ['Advanced applications', 'Troubleshooting'],
      practicalReadiness: passed ? 'Developing' : 'Needs support'
    }
  };
}

// Get next digital skills level for progression
function getNextDigitalSkillsLevel(currentLevel) {
  const levelProgression = {
    'EntryLevel2': 'EntryLevel2Plus',
    'EntryLevel2Plus': 'EntryLevel3',
    'EntryLevel3': 'Level1',
    'Level1': 'Level2',
    'Level2': 'Level3',
    'Level3': 'Level3' // Already at highest level
  };

  return levelProgression[currentLevel] || currentLevel;
}

// ============================================================================
// DATABASE STORAGE FUNCTIONS
// ============================================================================

// Store mathematics assessment results in database with comprehensive response logging
async function storeMathematicsAssessmentResults(email, level, analysisResult, answers, timeSpent, detailedResponses = null, userData = null) {
  try {
    const userCompany = 'Birmingham'; // Default company for student users

    // Prepare level-specific data
    const levelData = {
      completed: true,
      score: analysisResult.score,
      passed: analysisResult.passed,
      timeSpent: timeSpent || 0,
      completedAt: new Date(),
      responses: answers,
      topicBreakdown: analysisResult.topicBreakdown || {}
    };

    // Prepare update data with basic user information for document creation (matching main server structure)
    const updateData = {
      // Basic user information (in case document doesn't exist)
      userEmail: email,
      userCompany: userCompany,

      // Mathematics assessment data (matching main server structure)
      mathAssessmentCompleted: true,
      mathCurrentLevel: level,
      mathOverallScore: analysisResult.score,
      mathAssessmentTimestamp: new Date(),
      totalTimeSpentOnMath: timeSpent || 0,
      updatedAt: new Date(),

      // Store feedback in flattened structure
      mathFeedback: analysisResult.feedback || {},
      mathStrengths: analysisResult.strengths || [],
      mathImprovements: analysisResult.improvements || [],
      mathPlacementRecommendation: analysisResult.placementRecommendation || {}
    };

    // Add level-specific data (matching main server structure)
    switch (level) {
      case 'Entry':
        updateData.mathEntryLevel = levelData;
        updateData.mathHighestLevelCompleted = 'Entry';
        break;
      case 'Level1':
        updateData.mathLevel1 = levelData;
        updateData.mathHighestLevelCompleted = 'Level1';
        break;
      case 'GCSEPart1':
        updateData.mathGCSEPart1 = levelData;
        updateData.mathHighestLevelCompleted = 'GCSEPart1';
        break;
      case 'GCSEPart2':
        updateData.mathGCSEPart2 = levelData;
        updateData.mathHighestLevelCompleted = 'GCSEPart2';
        break;
    }

    // Add required dashboard fields if userData is provided
    if (userData) {
      updateData.firstName = userData.firstName;
      updateData.lastName = userData.lastName;
      updateData.name = userData.name || `${userData.firstName} ${userData.lastName}`;
      updateData.userType = userData.userType || 'student';
      updateData.studentLevel = userData.studentLevel;
      updateData.userEmail = email; // Required for dashboard
      updateData.userCompany = userCompany; // Required for dashboard
      updateData.status = 'completed'; // Set status as completed
      updateData.createdAt = updateData.createdAt || new Date(); // Set creation time if not exists

      console.log('Adding dashboard fields for user:', {
        name: updateData.name,
        userType: updateData.userType,
        studentLevel: updateData.studentLevel
      });
    }

    // Add detailed responses if provided (using flattened structure)
    if (detailedResponses) {
      updateData.mathDetailedResponses = {
        questionResponses: detailedResponses.questionResponses || [],
        interactionLog: detailedResponses.interactionLog || [],
        metadata: {
          userAgent: detailedResponses.userAgent || 'unknown',
          screenResolution: detailedResponses.screenResolution || 'unknown',
          timestamp: new Date(),
          responseCount: detailedResponses.questionResponses?.length || 0,
          interactionCount: detailedResponses.interactionLog?.length || 0
        }
      };

      console.log('Storing detailed responses:', {
        questionResponsesCount: detailedResponses.questionResponses?.length || 0,
        interactionLogCount: detailedResponses.interactionLog?.length || 0
      });
    }

    // Ensure company document exists (especially for Birmingham)
    const companyRef = firestore.collection('companies').doc(userCompany);
    const companyDoc = await companyRef.get();

    if (!companyDoc.exists && userCompany === 'Birmingham') {
      console.log('Creating Birmingham company document for mathematics assessment');
      await companyRef.set({
        name: 'Birmingham',
        type: 'student-focused',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        description: 'Auto-created company for student assessments'
      });
    }

    // Create or update user document (using set with merge to handle non-existent documents)
    const userRef = companyRef.collection('users').doc(email);

    console.log('Storing mathematics assessment data for user:', {
      email,
      company: userCompany,
      level,
      operation: 'set with merge'
    });

    // Use set with merge option to create document if it doesn't exist
    await userRef.set(updateData, { merge: true });

    console.log('Mathematics assessment results stored successfully:', {
      email,
      level,
      score: analysisResult.score,
      passed: analysisResult.passed,
      documentCreated: true,
      company: userCompany
    });

  } catch (error) {
    console.error('Error storing mathematics assessment results:', error);
    throw error;
  }
}

// Store digital skills assessment results in database with comprehensive analysis
async function storeDigitalSkillsAssessmentResults(email, level, analysisResult, answers, timeSpent, detailedResponses = null, userData = null) {
  try {
    const userCompany = 'Birmingham'; // Default company for student users

    // Prepare level-specific data
    const levelData = {
      completed: true,
      score: analysisResult.score,
      passed: analysisResult.passed,
      timeSpent: timeSpent || 0,
      completedAt: new Date(),
      responses: answers,
      topicBreakdown: analysisResult.topicBreakdown || {}
    };

    // Prepare update data with basic user information for document creation
    const updateData = {
      // Basic user information (in case document doesn't exist)
      userEmail: email,
      userCompany: userCompany,

      // Digital skills assessment data
      digitalSkillsAssessmentCompleted: true,
      digitalSkillsCurrentLevel: level,
      digitalSkillsOverallScore: analysisResult.score,
      digitalSkillsAssessmentTimestamp: new Date(),
      totalTimeSpentOnDigitalSkills: timeSpent || 0,
      updatedAt: new Date(),

      // Store comprehensive analysis in flattened structure
      digitalSkillsFeedback: analysisResult.feedback || {},
      digitalSkillsStrengths: analysisResult.strengths || [],
      digitalSkillsImprovements: analysisResult.improvements || [],
      digitalSkillsCourseRecommendation: analysisResult.courseRecommendation || {},
      digitalSkillsConfidenceAnalysis: analysisResult.confidenceAnalysis || {},
      digitalSkillsSkillsLevel: analysisResult.skillsLevel || level
    };

    // Add level-specific data using dynamic field names
    const levelFieldMap = {
      'EntryLevel2': 'digitalSkillsEntryLevel2',
      'EntryLevel2Plus': 'digitalSkillsEntryLevel2Plus',
      'EntryLevel3': 'digitalSkillsEntryLevel3',
      'Level1': 'digitalSkillsLevel1',
      'Level2': 'digitalSkillsLevel2',
      'Level3': 'digitalSkillsLevel3'
    };

    const levelFieldName = levelFieldMap[level];
    if (levelFieldName) {
      updateData[levelFieldName] = levelData;
      updateData.digitalSkillsHighestLevelCompleted = level;
    }

    // Add detailed responses if provided
    if (detailedResponses) {
      updateData.digitalSkillsDetailedResponses = {
        questionResponses: detailedResponses.questionResponses || [],
        assessmentMetadata: {
          ...detailedResponses.assessmentMetadata,
          analysisTimestamp: new Date(),
          aiAnalysisUsed: true
        },
        interactionLog: detailedResponses.interactionLog || []
      };
    }

    // Add user data if provided
    if (userData) {
      updateData.firstName = userData.firstName || updateData.firstName;
      updateData.lastName = userData.lastName || updateData.lastName;
      updateData.name = userData.name || updateData.name;
      updateData.studentLevel = userData.studentLevel || updateData.studentLevel;
      updateData.userType = userData.userType || 'student';
    }

    // Get company document reference
    const companyRef = firestore.collection('companies').doc(userCompany);

    // Verify company document exists
    const companyDoc = await companyRef.get();
    if (!companyDoc.exists) {
      console.log('Creating company document:', userCompany);
      await companyRef.set({
        name: userCompany,
        createdAt: new Date(),
        type: 'educational'
      });
    }

    // Create or update user document
    const userRef = companyRef.collection('users').doc(email);

    console.log('Storing digital skills assessment data for user:', {
      email,
      company: userCompany,
      level,
      operation: 'set with merge'
    });

    // Use set with merge option to create document if it doesn't exist
    await userRef.set(updateData, { merge: true });

    console.log('Digital skills assessment results stored successfully:', {
      email,
      level,
      score: analysisResult.score,
      passed: analysisResult.passed,
      skillsLevel: analysisResult.skillsLevel,
      documentCreated: true,
      company: userCompany
    });

  } catch (error) {
    console.error('Error storing digital skills assessment results:', error);
    throw error;
  }
}

// Initialize Birmingham company for student-focused version
async function initializeBirminghamCompany() {
  try {
    console.log('Checking Birmingham company setup...');
    const companyDoc = await firestore
      .collection('companies')
      .doc('Birmingham')
      .get();

    if (!companyDoc.exists) {
      console.log('Creating Birmingham company for student users...');
      await firestore
        .collection('companies')
        .doc('Birmingham')
        .set({
          name: 'Birmingham',
          type: 'student-focused',
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          description: 'Auto-created company for student assessments'
        });
      console.log('Birmingham company created successfully');
    } else {
      console.log('Birmingham company already exists');
    }
  } catch (error) {
    console.error('Error initializing Birmingham company:', error);
  }
}

// ============================================================================
// MATHEMATICS ASSESSMENT API ENDPOINTS
// ============================================================================

// Performance monitoring endpoint
app.get('/api/math-assessments/performance', (req, res) => {
  try {
    const metrics = getPerformanceMetrics();

    res.status(200).json({
      success: true,
      metrics: {
        ...metrics,
        cacheEntries: Array.from(mathQuestionCache.keys()),
        uptime: process.uptime(),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error getting performance metrics:', error);
    res.status(500).json({
      error: 'Failed to get performance metrics',
      details: error.message
    });
  }
});

// Health check endpoint with comprehensive diagnostics
app.get('/api/health', async (req, res) => {
  try {
    const metrics = getPerformanceMetrics();
    const startTime = Date.now();

    // Test OpenAI API connectivity
    let apiConnectivity = {
      status: 'unknown',
      responseTime: null,
      error: null
    };

    try {
      const testPromise = openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [{ role: "user", content: "Test connectivity - respond with 'OK'" }],
        max_tokens: 10,
        temperature: 0
      });

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Health check timeout')), 5000);
      });

      await Promise.race([testPromise, timeoutPromise]);
      apiConnectivity.status = 'healthy';
      apiConnectivity.responseTime = Date.now() - startTime;
    } catch (error) {
      apiConnectivity.status = 'unhealthy';
      apiConnectivity.error = error.message;
      apiConnectivity.responseTime = Date.now() - startTime;
    }

    // Determine overall health status
    const overallHealth = {
      status: 'healthy',
      issues: []
    };

    if (metrics.healthStatus === 'CRITICAL') {
      overallHealth.status = 'critical';
      overallHealth.issues.push(`${metrics.consecutiveFailures} consecutive API failures`);
    } else if (metrics.healthStatus === 'WARNING') {
      overallHealth.status = 'warning';
      overallHealth.issues.push(`${metrics.consecutiveFailures} consecutive API failures`);
    }

    if (apiConnectivity.status === 'unhealthy') {
      overallHealth.status = overallHealth.status === 'critical' ? 'critical' : 'warning';
      overallHealth.issues.push('OpenAI API connectivity issues');
    }

    if (parseFloat(metrics.timeoutRate) > 20) {
      overallHealth.status = overallHealth.status === 'critical' ? 'critical' : 'warning';
      overallHealth.issues.push(`High timeout rate: ${metrics.timeoutRate}`);
    }

    res.status(overallHealth.status === 'critical' ? 503 : 200).json({
      success: true,
      health: overallHealth,
      metrics: {
        ...metrics,
        apiConnectivity,
        cacheStatus: {
          mathQuestions: mathQuestionCache.size
        },
        requestQueue: getActiveRequestsStatus(),
        uptime: process.uptime(),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(500).json({
      success: false,
      health: { status: 'critical', issues: ['Health check system failure'] },
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Cache management endpoints
app.post('/api/math-assessments/cache/clear', (req, res) => {
  try {
    const cacheSize = mathQuestionCache.size;
    mathQuestionCache.clear();

    // Reset performance metrics
    resetPerformanceMetrics();

    res.status(200).json({
      success: true,
      message: `Cleared ${cacheSize} cache entries and reset performance metrics`
    });
  } catch (error) {
    console.error('Error clearing cache:', error);
    res.status(500).json({
      error: 'Failed to clear cache',
      details: error.message
    });
  }
});

// Database cache management endpoints
app.post('/api/cache/database/clear', async (req, res) => {
  try {
    console.log('Clearing database cache...');

    const snapshot = await firestore.collection('questionCache').get();
    const deletedCount = snapshot.size;

    if (deletedCount > 0) {
      const batch = firestore.batch();
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      await batch.commit();
    }

    // Also clear in-memory caches
    mathQuestionCache.clear();

    res.status(200).json({
      success: true,
      message: `Cleared ${deletedCount} database cache entries and all in-memory caches`
    });
  } catch (error) {
    console.error('Error clearing database cache:', error);
    res.status(500).json({
      error: 'Failed to clear database cache',
      details: error.message
    });
  }
});

app.post('/api/cache/database/cleanup', async (req, res) => {
  try {
    await cleanupExpiredDatabaseCache();
    res.status(200).json({
      success: true,
      message: 'Database cache cleanup completed'
    });
  } catch (error) {
    console.error('Error during database cache cleanup:', error);
    res.status(500).json({
      error: 'Failed to cleanup database cache',
      details: error.message
    });
  }
});

app.get('/api/cache/database/status', async (req, res) => {
  try {
    const snapshot = await firestore.collection('questionCache').get();
    const cacheEntries = [];
    const currentTime = Date.now();

    snapshot.forEach(doc => {
      const data = doc.data();
      const cacheAge = data.timestamp ? currentTime - data.timestamp.toDate().getTime() : null;
      const isExpired = cacheAge ? cacheAge > DATABASE_CACHE_EXPIRY_TIME : false;

      cacheEntries.push({
        id: doc.id,
        type: data.type,
        level: data.level,
        studentLevel: data.studentLevel,
        questionCount: data.questionCount,
        generatedAt: data.generatedAt,
        cacheAgeHours: cacheAge ? Math.round(cacheAge / (1000 * 60 * 60)) : null,
        isExpired: isExpired,
        expiresAt: data.expiresAt
      });
    });

    const expiredCount = cacheEntries.filter(entry => entry.isExpired).length;
    const activeCount = cacheEntries.length - expiredCount;

    res.status(200).json({
      success: true,
      summary: {
        totalEntries: cacheEntries.length,
        activeEntries: activeCount,
        expiredEntries: expiredCount,
        cacheExpiryDays: DATABASE_CACHE_EXPIRY_TIME / (1000 * 60 * 60 * 24)
      },
      entries: cacheEntries,
      inMemoryCache: {
        mathQuestions: mathQuestionCache.size
      }
    });
  } catch (error) {
    console.error('Error getting database cache status:', error);
    res.status(500).json({
      error: 'Failed to get database cache status',
      details: error.message
    });
  }
});

// Cache warming endpoint
app.post('/api/math-assessments/cache/warm', async (req, res) => {
  try {
    const levels = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];
    const studentLevels = ['adult-learner', 'returning-student'];
    const results = [];

    console.log('Starting cache warming...');

    for (const level of levels) {
      for (const studentLevel of studentLevels) {
        try {
          console.log(`Warming cache for ${level} - ${studentLevel}`);
          const questions = await generateMathematicsQuestions(level, studentLevel);
          results.push({
            level,
            studentLevel,
            questionCount: questions.length,
            status: 'success'
          });
        } catch (error) {
          console.error(`Failed to warm cache for ${level} - ${studentLevel}:`, error);
          results.push({
            level,
            studentLevel,
            status: 'failed',
            error: error.message
          });
        }
      }
    }

    res.status(200).json({
      success: true,
      message: 'Cache warming completed',
      results,
      cacheSize: mathQuestionCache.size
    });
  } catch (error) {
    console.error('Error warming cache:', error);
    res.status(500).json({
      error: 'Failed to warm cache',
      details: error.message
    });
  }
});



// Start Mathematics Assessment - Generate questions for specific level
app.post('/api/math-assessments/start', async (req, res) => {
  console.log('Received mathematics assessment start request');

  try {
    const { level, email, studentLevel } = req.body;

    // Validate required fields
    if (!level || !email) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: {
          level: !level ? 'Missing assessment level' : null,
          email: !email ? 'Missing email' : null
        }
      });
    }

    // Validate level
    const validLevels = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];
    if (!validLevels.includes(level)) {
      return res.status(400).json({
        error: 'Invalid assessment level',
        details: `Level must be one of: ${validLevels.join(', ')}`
      });
    }

    console.log('Generating mathematics questions for:', {
      email,
      level,
      studentLevel
    });

    // Generate questions using AI
    const questions = await generateMathematicsQuestions(level, studentLevel);

    // Create assessment session
    const assessmentId = `math_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    console.log('Mathematics assessment started:', {
      assessmentId,
      email,
      level,
      questionCount: questions.length
    });

    res.status(200).json({
      assessmentId,
      level,
      questions,
      timeLimit: getMathTimeLimit(level),
      passingScore: getMathPassingScore(level),
      maxScore: getMathMaxScore(level)
    });

  } catch (error) {
    console.error('Error starting mathematics assessment:', error);
    res.status(500).json({
      error: 'Failed to start mathematics assessment',
      details: error.message
    });
  }
});

// Submit Mathematics Assessment - Process answers and provide results
app.post('/api/math-assessments/:id/submit', async (req, res) => {
  console.log('Received mathematics assessment submission');

  try {
    const { id: assessmentId } = req.params;
    const { answers, email, level, timeSpent, detailedResponses, userData } = req.body;

    // Validate required fields
    if (!answers || !email || !level) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: {
          answers: !answers ? 'Missing answers' : null,
          email: !email ? 'Missing email' : null,
          level: !level ? 'Missing level' : null
        }
      });
    }

    console.log('Processing mathematics assessment submission:', {
      assessmentId,
      email,
      level,
      answerCount: answers.length,
      timeSpent: timeSpent || 'unknown',
      hasDetailedResponses: !!detailedResponses,
      detailedResponsesCount: detailedResponses?.questionResponses?.length || 0,
      interactionLogCount: detailedResponses?.interactionLog?.length || 0,
      hasUserData: !!userData,
      userDataFields: userData ? Object.keys(userData) : []
    });

    // Debug: Log the first few answers to understand the structure
    console.log('DEBUG - Sample answers structure:', {
      firstAnswer: answers[0],
      secondAnswer: answers[1],
      totalAnswers: answers.length
    });

    // Get the questions for this level to provide context for analysis
    const questions = await generateMathematicsQuestions(level, 'adult-learner');

    // Analyze answers with AI
    const analysisResult = await analyzeMathematicsAssessment(answers, level, timeSpent, questions);

    // Store results in database with comprehensive response logging
    await storeMathematicsAssessmentResults(email, level, analysisResult, answers, timeSpent, detailedResponses, userData);

    console.log('Mathematics assessment completed:', {
      assessmentId,
      email,
      level,
      score: analysisResult.score,
      passed: analysisResult.passed
    });

    res.status(200).json(analysisResult);

  } catch (error) {
    console.error('Error submitting mathematics assessment:', error);
    res.status(500).json({
      error: 'Failed to submit mathematics assessment',
      details: error.message
    });
  }
});

// Check user eligibility and progress for mathematics assessment
app.get('/api/math-progress/:email', async (req, res) => {
  try {
    const email = decodeURIComponent(req.params.email);
    console.log('Checking mathematics progress for user:', email);

    const companyRef = firestore.collection('companies').doc('Birmingham');
    const userRef = companyRef.collection('users').doc(email);
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
      return res.json({
        isNewUser: true,
        message: 'Welcome! You can start with Entry Level - Basic Mathematics',
        availableLevels: ['Entry']
      });
    }

    const userData = userDoc.data();

    // Extract mathematics assessment data
    const progressData = {};
    const levelSpecs = {
      'Entry': {
        name: "Entry Level - Basic Mathematics",
        description: "Basic arithmetic, fractions, percentages",
        order: 1,
        prerequisite: null,
        passingScore: 24,
        maxScore: 44,
        timeLimit: 30 * 60,
        questionCount: 22
      },
      'Level1': {
        name: "Level 1 - Intermediate Mathematics",
        description: "Advanced arithmetic, algebra, geometry",
        order: 2,
        prerequisite: 'Entry',
        passingScore: 16,
        maxScore: 26,
        timeLimit: 30 * 60,
        questionCount: 13
      },
      'GCSEPart1': {
        name: "GCSE Part 1 - Non-calculator",
        description: "Number operations, algebra without calculator",
        order: 3,
        prerequisite: 'Level1',
        passingScore: 5,
        maxScore: 10,
        timeLimit: 15 * 60,
        questionCount: 7
      },
      'GCSEPart2': {
        name: "GCSE Part 2 - Calculator",
        description: "Complex calculations, statistics, trigonometry",
        order: 4,
        prerequisite: 'GCSEPart1',
        passingScore: 8,
        maxScore: 20,
        timeLimit: 20 * 60,
        questionCount: 10
      }
    };

    // Process user's mathematics data
    Object.keys(userData).forEach(key => {
      if (key.startsWith('math') && key.includes('Level')) {
        // Extract level from key (e.g., 'mathEntryLevel' -> 'Entry')
        let levelKey = key.replace('math', '').replace('Level', '');
        if (levelKey === 'Entry') levelKey = 'Entry';
        else if (levelKey === '1') levelKey = 'Level1';
        else if (levelKey === 'GCSEPart1') levelKey = 'GCSEPart1';
        else if (levelKey === 'GCSEPart2') levelKey = 'GCSEPart2';

        if (levelSpecs[levelKey]) {
          progressData[levelKey] = {
            ...userData[key],
            ...levelSpecs[levelKey]
          };
        }
      }
    });

    // Determine available levels based on progress
    const availableLevels = [];
    const completedLevels = [];
    const failedLevels = [];

    // Check each level in order
    Object.keys(levelSpecs).forEach(levelKey => {
      const levelData = progressData[levelKey];
      const spec = levelSpecs[levelKey];

      if (levelData && levelData.completed) {
        if (levelData.passed) {
          completedLevels.push(levelKey);
        } else {
          failedLevels.push(levelKey);
        }
      }

      // Check if level is available
      if (!spec.prerequisite) {
        // First level is always available
        if (!levelData || !levelData.completed || !levelData.passed) {
          availableLevels.push(levelKey);
        }
      } else {
        // Check if prerequisite is completed and passed
        const prerequisiteData = progressData[spec.prerequisite];
        if (prerequisiteData && prerequisiteData.completed && prerequisiteData.passed) {
          if (!levelData || !levelData.completed || !levelData.passed) {
            availableLevels.push(levelKey);
          }
        }
      }
    });

    // Calculate statistics
    const totalCompleted = completedLevels.length;
    const totalScore = Object.values(progressData).reduce((sum, level) => {
      return sum + (level.score || 0);
    }, 0);
    const totalTimeSpent = Object.values(progressData).reduce((sum, level) => {
      return sum + (level.timeSpent || 0);
    }, 0);

    console.log('Mathematics progress retrieved:', {
      email,
      totalCompleted,
      availableLevels: availableLevels.length,
      isNewUser: Object.keys(progressData).length === 0
    });

    res.json({
      isNewUser: Object.keys(progressData).length === 0,
      progressData,
      availableLevels,
      completedLevels,
      failedLevels,
      statistics: {
        totalCompleted,
        totalScore,
        totalTimeSpent
      },
      levelSpecs
    });

  } catch (error) {
    console.error('Error fetching mathematics progress:', error);
    res.status(500).json({
      error: 'Failed to fetch user progress',
      details: error.message
    });
  }
});

// Get mathematics assessment analytics and statistics
app.get('/api/admin/math-analytics', async (req, res) => {
  try {
    const { company = 'Birmingham' } = req.query;

    const snapshot = await firestore
      .collection('companies')
      .doc(company)
      .collection('users')
      .where('mathAssessmentCompleted', '==', true)
      .get();

    const analytics = {
      totalAssessments: 0,
      levelBreakdown: {
        Entry: { completed: 0, passed: 0, averageScore: 0 },
        Level1: { completed: 0, passed: 0, averageScore: 0 },
        GCSEPart1: { completed: 0, passed: 0, averageScore: 0 },
        GCSEPart2: { completed: 0, passed: 0, averageScore: 0 }
      },
      overallPassRate: 0,
      averageTimeSpent: 0
    };

    let totalTimeSpent = 0;
    let totalPassed = 0;

    snapshot.forEach(doc => {
      const data = doc.data();
      if (data.mathAssessmentCompleted) {
        analytics.totalAssessments++;

        // Check each level using the flattened structure
        const levels = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];
        const levelFields = ['mathEntryLevel', 'mathLevel1', 'mathGCSEPart1', 'mathGCSEPart2'];

        levels.forEach((level, index) => {
          const levelData = data[levelFields[index]];
          if (levelData && levelData.completed) {
            analytics.levelBreakdown[level].completed++;
            if (levelData.passed) {
              analytics.levelBreakdown[level].passed++;
              totalPassed++;
            }
            analytics.levelBreakdown[level].averageScore += levelData.score || 0;
            totalTimeSpent += levelData.timeSpent || 0;
          }
        });
      }
    });

    // Calculate averages
    Object.keys(analytics.levelBreakdown).forEach(level => {
      const levelStats = analytics.levelBreakdown[level];
      if (levelStats.completed > 0) {
        levelStats.averageScore = Math.round(levelStats.averageScore / levelStats.completed);
        levelStats.passRate = Math.round((levelStats.passed / levelStats.completed) * 100);
      }
    });

    analytics.overallPassRate = analytics.totalAssessments > 0
      ? Math.round((totalPassed / analytics.totalAssessments) * 100)
      : 0;

    analytics.averageTimeSpent = analytics.totalAssessments > 0
      ? Math.round(totalTimeSpent / analytics.totalAssessments)
      : 0;

    res.status(200).json({
      success: true,
      analytics,
      company
    });

  } catch (error) {
    console.error('Error getting mathematics analytics:', error);
    res.status(500).json({
      error: 'Failed to get mathematics analytics',
      details: error.message
    });
  }
});

// ============================================================================
// DIGITAL SKILLS ASSESSMENT API ENDPOINTS
// ============================================================================

// Digital Skills Cache warming endpoint
app.post('/api/digital-skills-assessments/cache/warm', async (req, res) => {
  try {
    console.log('🚀 Manual digital skills cache warming initiated...');

    const levels = [
      'EntryLevel2',
      'EntryLevel2Plus',
      'Level1',
      'Level2',
      'EntryLevel3',
      'ICDLLevel2',
      'ICDLLevel3'
    ];

    let successCount = 0;
    let failureCount = 0;
    let cacheHitCount = 0;
    const results = [];

    for (const level of levels) {
      try {
        console.log(`🔄 Warming cache for digital skills level: ${level}`);

        // Force regeneration by checking cache first, then generating if needed
        const cachedQuestions = await getDatabaseCachedQuestions('digital', level);

        if (cachedQuestions && cachedQuestions.length > 0) {
          console.log(`✅ ${level} already cached (${cachedQuestions.length} questions)`);
          results.push({
            level,
            status: 'cache_hit',
            questionCount: cachedQuestions.length
          });
          cacheHitCount++;
        } else {
          const questions = await generateDigitalSkillsQuestions(level, null);

          if (questions && questions.length > 0) {
            console.log(`✅ Generated ${questions.length} questions for ${level}`);
            results.push({
              level,
              status: 'generated',
              questionCount: questions.length
            });
            successCount++;
          } else {
            throw new Error('No questions generated');
          }
        }
      } catch (error) {
        console.error(`❌ Failed to warm cache for ${level}:`, error.message);
        results.push({
          level,
          status: 'failed',
          error: error.message
        });
        failureCount++;
      }
    }

    res.status(200).json({
      success: true,
      message: 'Digital skills cache warming completed',
      summary: {
        totalLevels: levels.length,
        cacheHits: cacheHitCount,
        generated: successCount,
        failures: failureCount
      },
      results
    });
  } catch (error) {
    console.error('Error warming digital skills cache:', error);
    res.status(500).json({
      error: 'Failed to warm digital skills cache',
      details: error.message
    });
  }
});

// Start Digital Skills Assessment - Generate questions for the specified level
app.post('/api/digital-skills-assessments/start', async (req, res) => {
  console.log('Received digital skills assessment start request');

  try {
    const { level, email, studentLevel } = req.body;

    // Validate required fields
    if (!level || !email) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: {
          level: !level ? 'Missing level' : null,
          email: !email ? 'Missing email' : null
        }
      });
    }

    console.log('Starting digital skills assessment:', {
      level,
      email,
      studentLevel
    });

    // Generate questions using AI
    const questions = await generateDigitalSkillsQuestions(level, studentLevel);

    // Create assessment session
    const assessmentId = `digital_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    console.log('Digital skills assessment started:', {
      assessmentId,
      email,
      level,
      questionCount: questions.length
    });

    res.status(200).json({
      assessmentId,
      level,
      questions,
      timeLimit: getDigitalSkillsTimeLimit(level),
      passingScore: getDigitalSkillsPassingScore(level),
      maxScore: getDigitalSkillsMaxScore(level)
    });

  } catch (error) {
    console.error('Error starting digital skills assessment:', error);
    res.status(500).json({
      error: 'Failed to start digital skills assessment',
      details: error.message
    });
  }
});

// Submit Digital Skills Assessment - Process answers and provide results
app.post('/api/digital-skills-assessments/:id/submit', async (req, res) => {
  console.log('Received digital skills assessment submission');

  try {
    const { id: assessmentId } = req.params;
    const { answers, email, level, timeSpent, detailedResponses, userData } = req.body;

    // Validate required fields
    if (!answers || !email || !level) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: {
          answers: !answers ? 'Missing answers' : null,
          email: !email ? 'Missing email' : null,
          level: !level ? 'Missing level' : null
        }
      });
    }

    console.log('Processing digital skills assessment submission:', {
      assessmentId,
      email,
      level,
      answerCount: answers.length,
      timeSpent,
      hasDetailedResponses: !!detailedResponses,
      detailedResponsesCount: detailedResponses?.questionResponses?.length || 0,
      interactionLogCount: detailedResponses?.interactionLog?.length || 0,
      hasUserData: !!userData,
      userDataFields: userData ? Object.keys(userData) : []
    });

    // Debug: Log the first few answers to understand the structure
    console.log('DEBUG - Sample digital skills answers structure:', {
      firstAnswer: answers[0],
      secondAnswer: answers[1],
      totalAnswers: answers.length
    });

    // Get the questions for this level to provide context for analysis
    const questions = await generateDigitalSkillsQuestions(level, userData?.studentLevel || 'adult-learner');

    // Analyze answers with AI
    const analysisResult = await analyzeDigitalSkillsAssessment(answers, level, timeSpent, questions);

    // Store results in database with comprehensive analysis
    await storeDigitalSkillsAssessmentResults(email, level, analysisResult, answers, timeSpent, detailedResponses, userData);

    console.log('Digital skills assessment completed:', {
      assessmentId,
      email,
      level,
      score: analysisResult.score,
      passed: analysisResult.passed
    });

    res.status(200).json(analysisResult);

  } catch (error) {
    console.error('Error submitting digital skills assessment:', error);
    res.status(500).json({
      error: 'Failed to submit digital skills assessment',
      details: error.message
    });
  }
});

// ============================================================================
// ADMIN DASHBOARD API ENDPOINTS
// ============================================================================

// Check user eligibility and progress for digital skills assessment
app.get('/api/digital-skills-progress/:email', async (req, res) => {
  try {
    const email = decodeURIComponent(req.params.email);
    console.log('Checking digital skills progress for user:', email);

    const companyRef = firestore.collection('companies').doc('Birmingham');
    const userRef = companyRef.collection('users').doc(email);
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
      return res.json({
        isNewUser: true,
        message: 'Welcome! You can start with Entry Level 2 - Computer Skills Beginners',
        availableLevels: ['EntryLevel2']
      });
    }

    const userData = userDoc.data();

    // Extract digital skills assessment data
    const progressData = {};
    const levelSpecs = {
      'EntryLevel2': {
        name: "Entry Level 2 - Computer Skills Beginners",
        description: "Basic computer parts, mouse/keyboard, simple operations",
        order: 1,
        prerequisite: null,
        passingScore: 12,
        maxScore: 30
      },
      'EntryLevel2Plus': {
        name: "Entry Level 2/3 - Computer Skills Beginners Plus",
        description: "Laptops/desktops, apps, internet safety, email",
        order: 2,
        prerequisite: 'EntryLevel2',
        passingScore: 15,
        maxScore: 36
      },
      'EntryLevel3': {
        name: "Entry Level 3 - Improvers Plus",
        description: "File management, internet research, online forms",
        order: 3,
        prerequisite: 'EntryLevel2Plus',
        passingScore: 18,
        maxScore: 42
      },
      'Level1': {
        name: "Level 1 - Everyday Life Level 1",
        description: "Advanced internet, online shopping, digital communication",
        order: 4,
        prerequisite: 'EntryLevel3',
        passingScore: 21,
        maxScore: 48
      },
      'Level2': {
        name: "Level 2 - ICDL Level 2",
        description: "Professional digital skills, advanced applications",
        order: 5,
        prerequisite: 'Level1',
        passingScore: 24,
        maxScore: 54
      },
      'Level3': {
        name: "Level 3 - Computer Skills for Work Level 2",
        description: "Workplace digital skills, advanced productivity",
        order: 6,
        prerequisite: 'Level2',
        passingScore: 27,
        maxScore: 60
      }
    };

    // Process user's digital skills data
    Object.keys(userData).forEach(key => {
      if (key.startsWith('digitalSkills')) {
        const levelKey = key.replace('digitalSkills', '');
        if (levelSpecs[levelKey]) {
          progressData[levelKey] = {
            ...userData[key],
            ...levelSpecs[levelKey]
          };
        }
      }
    });

    // Determine available levels based on progress
    const availableLevels = [];
    const completedLevels = [];
    const failedLevels = [];

    // Check each level in order
    Object.keys(levelSpecs).forEach(levelKey => {
      const levelData = progressData[levelKey];
      const spec = levelSpecs[levelKey];

      if (levelData && levelData.completed) {
        if (levelData.passed) {
          completedLevels.push(levelKey);
        } else {
          failedLevels.push(levelKey);
        }
      }

      // Check if level is available
      if (!spec.prerequisite) {
        // First level is always available
        if (!levelData || !levelData.completed || !levelData.passed) {
          availableLevels.push(levelKey);
        }
      } else {
        // Check if prerequisite is completed and passed
        const prerequisiteData = progressData[spec.prerequisite];
        if (prerequisiteData && prerequisiteData.completed && prerequisiteData.passed) {
          if (!levelData || !levelData.completed || !levelData.passed) {
            availableLevels.push(levelKey);
          }
        }
      }
    });

    // Calculate statistics
    const totalCompleted = completedLevels.length;
    const totalScore = Object.values(progressData).reduce((sum, level) => {
      return sum + (level.score || 0);
    }, 0);
    const totalTimeSpent = Object.values(progressData).reduce((sum, level) => {
      return sum + (level.timeSpent || 0);
    }, 0);

    console.log('Digital skills progress retrieved:', {
      email,
      totalCompleted,
      availableLevels: availableLevels.length,
      isNewUser: Object.keys(progressData).length === 0
    });

    res.json({
      isNewUser: Object.keys(progressData).length === 0,
      progressData,
      availableLevels,
      completedLevels,
      failedLevels,
      statistics: {
        totalCompleted,
        totalScore,
        totalTimeSpent
      },
      levelSpecs
    });

  } catch (error) {
    console.error('Error fetching digital skills progress:', error);
    res.status(500).json({
      error: 'Failed to fetch user progress',
      details: error.message
    });
  }
});

// Get digital skills assessment analytics and statistics
app.get('/api/admin/digital-skills-analytics', async (req, res) => {
  try {
    const { company = 'Birmingham' } = req.query;

    const snapshot = await firestore
      .collection('companies')
      .doc(company)
      .collection('users')
      .where('digitalSkillsAssessmentCompleted', '==', true)
      .get();

    const analytics = {
      totalAssessments: 0,
      levelBreakdown: {
        EntryLevel2: { completed: 0, passed: 0, averageScore: 0 },
        EntryLevel2Plus: { completed: 0, passed: 0, averageScore: 0 },
        EntryLevel3: { completed: 0, passed: 0, averageScore: 0 },
        Level1: { completed: 0, passed: 0, averageScore: 0 },
        Level2: { completed: 0, passed: 0, averageScore: 0 },
        Level3: { completed: 0, passed: 0, averageScore: 0 }
      },
      overallPassRate: 0,
      averageTimeSpent: 0,
      skillsLevelDistribution: {},
      confidenceAnalysis: {
        highConfidence: 0,
        mediumConfidence: 0,
        lowConfidence: 0
      }
    };

    let totalTimeSpent = 0;
    let totalPassed = 0;
    let totalScores = { EntryLevel2: 0, EntryLevel2Plus: 0, EntryLevel3: 0, Level1: 0, Level2: 0, Level3: 0 };
    let scoreCounts = { EntryLevel2: 0, EntryLevel2Plus: 0, EntryLevel3: 0, Level1: 0, Level2: 0, Level3: 0 };

    snapshot.forEach(doc => {
      const data = doc.data();
      analytics.totalAssessments++;

      const currentLevel = data.digitalSkillsCurrentLevel;
      const overallScore = data.digitalSkillsOverallScore || 0;
      const timeSpent = data.totalTimeSpentOnDigitalSkills || 0;
      const skillsLevel = data.digitalSkillsSkillsLevel;
      const confidenceAnalysis = data.digitalSkillsConfidenceAnalysis;

      // Level breakdown
      if (currentLevel && analytics.levelBreakdown[currentLevel]) {
        analytics.levelBreakdown[currentLevel].completed++;
        totalScores[currentLevel] += overallScore;
        scoreCounts[currentLevel]++;

        // Check if passed (simplified - would need level-specific logic)
        const levelSpecs = getDigitalSkillsQuestionSpecs(currentLevel);
        if (overallScore >= levelSpecs.passingScore) {
          analytics.levelBreakdown[currentLevel].passed++;
          totalPassed++;
        }
      }

      // Skills level distribution
      if (skillsLevel) {
        analytics.skillsLevelDistribution[skillsLevel] = (analytics.skillsLevelDistribution[skillsLevel] || 0) + 1;
      }

      // Confidence analysis
      if (confidenceAnalysis && confidenceAnalysis.overallConfidence) {
        const confidence = confidenceAnalysis.overallConfidence.toLowerCase();
        if (confidence === 'high') analytics.confidenceAnalysis.highConfidence++;
        else if (confidence === 'medium') analytics.confidenceAnalysis.mediumConfidence++;
        else if (confidence === 'low') analytics.confidenceAnalysis.lowConfidence++;
      }

      totalTimeSpent += timeSpent;
    });

    // Calculate averages
    Object.keys(analytics.levelBreakdown).forEach(level => {
      if (scoreCounts[level] > 0) {
        analytics.levelBreakdown[level].averageScore = Math.round(totalScores[level] / scoreCounts[level]);
      }
    });

    analytics.overallPassRate = analytics.totalAssessments > 0 ?
      Math.round((totalPassed / analytics.totalAssessments) * 100) : 0;
    analytics.averageTimeSpent = analytics.totalAssessments > 0 ?
      Math.round(totalTimeSpent / analytics.totalAssessments) : 0;

    res.status(200).json({
      success: true,
      data: analytics
    });

  } catch (error) {
    console.error('Error fetching digital skills analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch digital skills analytics',
      details: error.message
    });
  }
});

// Get digital skills assessment responses for admin review
app.get('/api/admin/digital-skills-responses', async (req, res) => {
  try {
    const { company = 'Birmingham', limit = 50 } = req.query;

    const snapshot = await firestore
      .collection('companies')
      .doc(company)
      .collection('users')
      .where('digitalSkillsAssessmentCompleted', '==', true)
      .orderBy('digitalSkillsAssessmentTimestamp', 'desc')
      .limit(parseInt(limit))
      .get();

    const responses = [];

    snapshot.forEach(doc => {
      const data = doc.data();
      responses.push({
        userEmail: doc.id,
        name: data.name || `${data.firstName || ''} ${data.lastName || ''}`.trim() || 'Unknown',
        currentLevel: data.digitalSkillsCurrentLevel,
        overallScore: data.digitalSkillsOverallScore,
        skillsLevel: data.digitalSkillsSkillsLevel,
        completedAt: data.digitalSkillsAssessmentTimestamp,
        timeSpent: data.totalTimeSpentOnDigitalSkills,
        feedback: data.digitalSkillsFeedback,
        strengths: data.digitalSkillsStrengths,
        improvements: data.digitalSkillsImprovements,
        courseRecommendation: data.digitalSkillsCourseRecommendation,
        confidenceAnalysis: data.digitalSkillsConfidenceAnalysis
      });
    });

    res.status(200).json({
      success: true,
      data: responses
    });

  } catch (error) {
    console.error('Error fetching digital skills responses:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch digital skills responses',
      details: error.message
    });
  }
});

// Get detailed digital skills assessment response for specific user
app.get('/api/admin/digital-skills-responses/:email', async (req, res) => {
  try {
    const { email } = req.params;
    const { company = 'Birmingham' } = req.query;

    const userDoc = await firestore
      .collection('companies')
      .doc(company)
      .collection('users')
      .doc(email)
      .get();

    if (!userDoc.exists) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    const data = userDoc.data();

    if (!data.digitalSkillsAssessmentCompleted) {
      return res.status(404).json({
        success: false,
        error: 'Digital skills assessment not completed by this user'
      });
    }

    const response = {
      userEmail: email,
      name: data.name || `${data.firstName || ''} ${data.lastName || ''}`.trim() || 'Unknown',
      assessmentCompleted: data.digitalSkillsAssessmentCompleted,
      currentLevel: data.digitalSkillsCurrentLevel,
      overallScore: data.digitalSkillsOverallScore,
      skillsLevel: data.digitalSkillsSkillsLevel,
      highestLevelCompleted: data.digitalSkillsHighestLevelCompleted,
      completedAt: data.digitalSkillsAssessmentTimestamp,
      totalTimeSpent: data.totalTimeSpentOnDigitalSkills,
      feedback: data.digitalSkillsFeedback,
      strengths: data.digitalSkillsStrengths,
      improvements: data.digitalSkillsImprovements,
      courseRecommendation: data.digitalSkillsCourseRecommendation,
      confidenceAnalysis: data.digitalSkillsConfidenceAnalysis,
      detailedResponses: data.digitalSkillsDetailedResponses,
      // Include level-specific data
      entryLevel2: data.digitalSkillsEntryLevel2,
      entryLevel2Plus: data.digitalSkillsEntryLevel2Plus,
      entryLevel3: data.digitalSkillsEntryLevel3,
      level1: data.digitalSkillsLevel1,
      level2: data.digitalSkillsLevel2,
      level3: data.digitalSkillsLevel3
    };

    res.status(200).json({
      success: true,
      data: response
    });

  } catch (error) {
    console.error('Error fetching digital skills response for user:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch digital skills response',
      details: error.message
    });
  }
});

// Root endpoint - serve the mathematics assessment page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'math.html'));
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: 'Assessment Server',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    cacheSize: {
      mathematics: mathQuestionCache.size,
      total: mathQuestionCache.size
    },
    performanceMetrics: getPerformanceMetrics()
  });
});

// ============================================================================
// SERVER STARTUP
// ============================================================================

// Preload mathematics question cache for better performance with detailed monitoring
async function preloadMathQuestionCache() {
  if (!PRELOAD_CACHE_ON_STARTUP) {
    console.log('Math cache preloading disabled');
    return;
  }

  const startTime = Date.now();
  console.log('🚀 Starting mathematics question cache preloading...');
  const levels = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];
  const studentLevels = ['adult-learner', 'returning-student'];

  let preloadPromises = [];
  let activePreloads = 0;
  let successCount = 0;
  let failureCount = 0;
  const preloadResults = [];

  for (const level of levels) {
    for (const studentLevel of studentLevels) {
      // Limit concurrent preloads
      if (activePreloads >= CONCURRENT_PRELOAD_LIMIT) {
        await Promise.race(preloadPromises);
        activePreloads--;
      }

      const itemStartTime = Date.now();
      const preloadPromise = generateMathematicsQuestions(level, studentLevel, REQUEST_PRIORITY.PRELOAD)
        .then(() => {
          const duration = Date.now() - itemStartTime;
          console.log(`✅ Preloaded math cache for ${level} - ${studentLevel} in ${duration}ms`);
          preloadResults.push({ level, studentLevel, status: 'success', duration });
          successCount++;
          activePreloads--;
        })
        .catch(error => {
          const duration = Date.now() - itemStartTime;
          console.warn(`⚠️ Failed to preload math ${level} - ${studentLevel} in ${duration}ms:`, error.message);
          preloadResults.push({ level, studentLevel, status: 'failed', duration, error: error.message });
          failureCount++;
          activePreloads--;
        });

      preloadPromises.push(preloadPromise);
      activePreloads++;
    }
  }

  // Wait for all preloads to complete
  await Promise.allSettled(preloadPromises);
  const totalDuration = Date.now() - startTime;

  console.log(`🎯 Math cache preloading completed in ${totalDuration}ms`);
  console.log(`📊 Math preload results: ${successCount} success, ${failureCount} failures, cache size: ${mathQuestionCache.size}`);

  if (failureCount > 0) {
    console.warn(`⚠️ Math cache preload failures:`, preloadResults.filter(r => r.status === 'failed'));
  }
}

// Preload digital skills question cache for all levels on server startup
async function preloadDigitalSkillsQuestionCache() {
  if (!PRELOAD_CACHE_ON_STARTUP) {
    console.log('Digital skills cache preloading disabled');
    return;
  }

  const startTime = Date.now();
  console.log('🚀 Starting digital skills question cache preloading...');

  // All digital skills levels that need to be cached
  const levels = [
    'EntryLevel2',
    'EntryLevel2Plus',
    'Level1',
    'Level2',
    'EntryLevel3',
    'ICDLLevel2',
    'ICDLLevel3'
  ];

  let successCount = 0;
  let failureCount = 0;
  let cacheHitCount = 0;
  const preloadResults = [];

  console.log(`📋 Checking database cache for ${levels.length} digital skills levels...`);

  for (const level of levels) {
    const itemStartTime = Date.now();

    try {
      console.log(`🔍 Checking cache for digital skills level: ${level}`);

      // Check if questions exist in database cache and are still valid
      const cacheKey = `digital_skills_${level}`;
      const cachedQuestions = await getDatabaseCachedQuestions('digital', level);

      if (cachedQuestions && cachedQuestions.length > 0) {
        const duration = Date.now() - itemStartTime;
        console.log(`✅ Digital skills ${level} found in database cache (${cachedQuestions.length} questions) - ${duration}ms`);
        preloadResults.push({
          level,
          status: 'cache_hit',
          duration,
          questionCount: cachedQuestions.length
        });
        cacheHitCount++;
      } else {
        // Cache miss or expired - generate new questions
        console.log(`🔄 Cache miss for ${level} - generating new questions...`);

        const questions = await generateDigitalSkillsQuestions(level, null);
        const duration = Date.now() - itemStartTime;

        if (questions && questions.length > 0) {
          console.log(`✅ Generated and cached ${questions.length} questions for digital skills ${level} - ${duration}ms`);
          preloadResults.push({
            level,
            status: 'generated',
            duration,
            questionCount: questions.length
          });
          successCount++;
        } else {
          throw new Error('No questions generated');
        }
      }
    } catch (error) {
      const duration = Date.now() - itemStartTime;
      console.error(`❌ Failed to preload digital skills ${level} in ${duration}ms:`, error.message);
      preloadResults.push({
        level,
        status: 'failed',
        duration,
        error: error.message
      });
      failureCount++;
    }
  }

  const totalDuration = Date.now() - startTime;
  console.log(`🎯 Digital skills cache preloading completed in ${totalDuration}ms`);
  console.log(`📊 Digital skills preload results:`);
  console.log(`   - Cache hits: ${cacheHitCount}`);
  console.log(`   - Generated: ${successCount}`);
  console.log(`   - Failures: ${failureCount}`);
  console.log(`   - Total levels processed: ${levels.length}`);

  if (failureCount > 0) {
    console.warn(`⚠️ Digital skills cache preload failures:`, preloadResults.filter(r => r.status === 'failed'));
  }

  // Log detailed results for monitoring
  console.log(`📋 Detailed preload results:`, preloadResults);
}

// Start the server
app.listen(port, async () => {
  console.log(`🧮 Assessment Server is running on http://localhost:${port}`);
  console.log(`📊 Performance monitoring available at http://localhost:${port}/api/math-assessments/performance`);
  console.log(`🏥 Health check available at http://localhost:${port}/api/health`);

  // Debug: Check initial performance metrics
  console.log('Initial performance metrics:', JSON.stringify(getPerformanceMetrics(), null, 2));

  // Initialize Birmingham company for student-focused version
  await initializeBirminghamCompany();

  // Schedule database cache cleanup (run every 24 hours)
  console.log('📅 Scheduling database cache cleanup...');
  setInterval(async () => {
    console.log('🧹 Running scheduled database cache cleanup...');
    try {
      await cleanupExpiredDatabaseCache();
    } catch (error) {
      console.error('Scheduled cache cleanup failed:', error.message);
    }
  }, 24 * 60 * 60 * 1000); // 24 hours

  // Run initial cleanup
  console.log('🧹 Running initial database cache cleanup...');
  try {
    await cleanupExpiredDatabaseCache();
  } catch (error) {
    console.warn('Initial cache cleanup failed:', error.message);
  }

  // Preload question caches for better performance
  console.log('🚀 Starting question cache preloading...');
  await Promise.allSettled([
    preloadMathQuestionCache(),
    preloadDigitalSkillsQuestionCache()
  ]);

  console.log('🎯 Assessment Server ready for use!');
});
