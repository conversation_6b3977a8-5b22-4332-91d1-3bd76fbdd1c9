<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Arithmetic Assessment - Creative Questions</title>
    <link rel="stylesheet" href="mathInteractive.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
            background: #f9fafb;
            line-height: 1.6;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: linear-gradient(135deg, #1547bb, #3b82f6);
            color: white;
            border-radius: 12px;
        }
        
        .comparison-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .question-card {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #1547bb;
        }
        
        .old-question {
            border-left-color: #ef4444;
        }
        
        .new-question {
            border-left-color: #10b981;
        }
        
        .question-type {
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 1rem;
        }
        
        .old-question .question-type {
            color: #ef4444;
        }
        
        .new-question .question-type {
            color: #10b981;
        }
        
        .question-text {
            font-size: 1.1rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 1.5rem;
        }
        
        .options {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin: 1rem 0;
        }
        
        .option {
            padding: 0.75rem;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .option:hover {
            border-color: #1547bb;
            background: #f0f7ff;
        }
        
        .explanation {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            font-size: 0.9rem;
            color: #1e40af;
        }
        
        .skills-tested {
            background: #f9fafb;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .skills-tested h4 {
            margin: 0 0 0.5rem 0;
            color: #374151;
            font-size: 0.9rem;
        }
        
        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        
        .skill-tag {
            background: #e0e7ff;
            color: #3730a3;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .improvement-summary {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin: 3rem 0;
        }
        
        .improvement-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }
        
        .improvement-item {
            padding: 1.5rem;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #1547bb;
        }
        
        .improvement-item h4 {
            color: #1547bb;
            margin: 0 0 0.5rem 0;
        }
        
        @media (max-width: 768px) {
            .comparison-section {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧮 Enhanced Arithmetic Assessment</h1>
        <p>Moving beyond "What is X + Y?" to creative, contextual, and challenging arithmetic questions</p>
    </div>

    <h2>Before vs After: Question Quality Comparison</h2>

    <!-- Example 1: Basic Addition -->
    <div class="comparison-section">
        <div class="question-card old-question">
            <div class="question-type">❌ Old Approach</div>
            <div class="question-text">What is 25 + 37?</div>
            <div class="options">
                <div class="option">A) 52</div>
                <div class="option">B) 62 ✓</div>
                <div class="option">C) 72</div>
                <div class="option">D) 82</div>
            </div>
            <div class="skills-tested">
                <h4>Skills Tested:</h4>
                <div class="skills-list">
                    <span class="skill-tag">Basic Addition</span>
                </div>
            </div>
        </div>

        <div class="question-card new-question">
            <div class="question-type">✅ New Approach</div>
            <div class="question-text">A recipe calls for 250g of flour, but you only have a 150g bag and a 75g bag. How much more flour do you need?</div>
            <div class="options">
                <div class="option">A) 25g ✓</div>
                <div class="option">B) 50g</div>
                <div class="option">C) 75g</div>
                <div class="option">D) 100g</div>
            </div>
            <div class="explanation">
                You have 150g + 75g = 225g. You need 250g - 225g = 25g more.
            </div>
            <div class="skills-tested">
                <h4>Skills Tested:</h4>
                <div class="skills-list">
                    <span class="skill-tag">Multi-step Problem</span>
                    <span class="skill-tag">Real-world Context</span>
                    <span class="skill-tag">Addition & Subtraction</span>
                    <span class="skill-tag">Planning</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Example 2: Multiplication -->
    <div class="comparison-section">
        <div class="question-card old-question">
            <div class="question-type">❌ Old Approach</div>
            <div class="question-text">What is 6 × 9?</div>
            <div class="options">
                <div class="option">A) 52</div>
                <div class="option">B) 54 ✓</div>
                <div class="option">C) 56</div>
                <div class="option">D) 58</div>
            </div>
            <div class="skills-tested">
                <h4>Skills Tested:</h4>
                <div class="skills-list">
                    <span class="skill-tag">Times Tables</span>
                </div>
            </div>
        </div>

        <div class="question-card new-question">
            <div class="question-type">✅ New Approach</div>
            <div class="question-text">Which calculation gives the largest result?</div>
            <div class="options">
                <div class="option">A) 7 × 8</div>
                <div class="option">B) 9 × 6</div>
                <div class="option">C) 4 × 15 ✓</div>
                <div class="option">D) 12 × 5</div>
            </div>
            <div class="explanation">
                7×8=56, 9×6=54, 4×15=60, 12×5=60. Both 4×15 and 12×5 equal 60, which is largest.
            </div>
            <div class="skills-tested">
                <h4>Skills Tested:</h4>
                <div class="skills-list">
                    <span class="skill-tag">Comparison</span>
                    <span class="skill-tag">Mental Math</span>
                    <span class="skill-tag">Multiple Calculations</span>
                    <span class="skill-tag">Critical Thinking</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Example 3: Division -->
    <div class="comparison-section">
        <div class="question-card old-question">
            <div class="question-type">❌ Old Approach</div>
            <div class="question-text">What is 84 - 29?</div>
            <div class="options">
                <div class="option">A) 45</div>
                <div class="option">B) 55 ✓</div>
                <div class="option">C) 65</div>
                <div class="option">D) 75</div>
            </div>
            <div class="skills-tested">
                <h4>Skills Tested:</h4>
                <div class="skills-list">
                    <span class="skill-tag">Basic Subtraction</span>
                </div>
            </div>
        </div>

        <div class="question-card new-question">
            <div class="question-type">✅ New Approach</div>
            <div class="question-text">If you save £8 each week, how many weeks will it take to save £100?</div>
            <div class="options">
                <div class="option">A) 11 weeks</div>
                <div class="option">B) 12 weeks</div>
                <div class="option">C) 13 weeks ✓</div>
                <div class="option">D) 14 weeks</div>
            </div>
            <div class="explanation">
                £100 ÷ £8 = 12.5 weeks, so you need 13 complete weeks to have at least £100.
            </div>
            <div class="skills-tested">
                <h4>Skills Tested:</h4>
                <div class="skills-list">
                    <span class="skill-tag">Division</span>
                    <span class="skill-tag">Rounding Up</span>
                    <span class="skill-tag">Real-world Context</span>
                    <span class="skill-tag">Logical Reasoning</span>
                </div>
            </div>
        </div>
    </div>

    <div class="improvement-summary">
        <h2>🎯 Key Improvements in Arithmetic Assessment</h2>
        <div class="improvement-list">
            <div class="improvement-item">
                <h4>Real-World Contexts</h4>
                <p>Questions now use practical scenarios like shopping, cooking, saving money, and everyday calculations that adults actually encounter.</p>
            </div>
            
            <div class="improvement-item">
                <h4>Multi-Step Problem Solving</h4>
                <p>Instead of single operations, questions require planning, multiple steps, and strategic thinking to reach the solution.</p>
            </div>
            
            <div class="improvement-item">
                <h4>Estimation & Approximation</h4>
                <p>Questions test ability to estimate, round numbers strategically, and check if answers are reasonable.</p>
            </div>
            
            <div class="improvement-item">
                <h4>Inverse Thinking</h4>
                <p>Questions like "What number times 8 equals 72?" test understanding of relationships between operations.</p>
            </div>
            
            <div class="improvement-item">
                <h4>Comparison & Analysis</h4>
                <p>Questions asking "Which is larger?" or "Order from smallest to largest" develop number sense and analytical skills.</p>
            </div>
            
            <div class="improvement-item">
                <h4>Mental Math Strategies</h4>
                <p>Questions encourage different approaches to calculation, showing that there are multiple valid methods.</p>
            </div>
        </div>
    </div>

    <div class="improvement-summary">
        <h2>📊 Assessment Quality Metrics</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
            <div style="text-align: center; padding: 1rem; background: #fef2f2; border-radius: 8px;">
                <div style="font-size: 2rem; color: #dc2626;">❌</div>
                <div style="font-weight: 600; color: #dc2626;">Old Questions</div>
                <div style="font-size: 0.9rem; color: #6b7280;">1 skill per question</div>
            </div>
            <div style="text-align: center; padding: 1rem; background: #f0fdf4; border-radius: 8px;">
                <div style="font-size: 2rem; color: #16a34a;">✅</div>
                <div style="font-weight: 600; color: #16a34a;">New Questions</div>
                <div style="font-size: 0.9rem; color: #6b7280;">3-5 skills per question</div>
            </div>
        </div>
    </div>
</body>
</html>
