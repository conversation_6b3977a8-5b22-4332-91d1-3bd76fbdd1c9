<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Next Button - Drag and Drop</title>
    <link rel="stylesheet" href="mathInteractive.css">
    <style>
        body {
            font-family: Inter, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f9f9f9;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .button-test {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        #next-question-btn {
            background: #1547bb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        #next-question-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }
        
        #next-question-btn:not(:disabled):hover {
            background: #0d3a8c;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .status.enabled {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.disabled {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Test Next Button - Drag and Drop</h1>
    
    <div class="test-container">
        <h2>Drag and Drop Question - Object Format Test</h2>
        <p>Match each fraction to its decimal equivalent:</p>
        <p><strong>Testing:</strong> This tests the fix for "[object Object]" display issue</p>

        <!-- Drag and Drop Container -->
        <div id="drag-drop-container" class="interactive-container">
            <div class="drag-drop-area">
                <div class="draggable-items" id="draggable-items">
                    <!-- Items will be populated by JavaScript -->
                </div>

                <div class="drop-zones" id="drop-zones">
                    <!-- Zones will be populated by JavaScript -->
                </div>
            </div>
        </div>
        
        <div class="button-test">
            <button id="next-question-btn" disabled>Next Question</button>
            <div id="button-status" class="status disabled">Button Status: DISABLED</div>
        </div>
    </div>
    
    <script>
        // Test implementation using object format (like the real assessment)
        class DragDropTest {
            constructor() {
                // Test with object format that was causing "[object Object]" issue
                const testQuestion = {
                    type: "drag-drop",
                    question: "Match each fraction to its decimal equivalent.",
                    dragDropConfig: {
                        items: [
                            { id: "frac1", text: "1/2" },
                            { id: "frac2", text: "1/4" },
                            { id: "frac3", text: "3/4" }
                        ],
                        zones: [
                            { id: "dec1", label: "0.5" },
                            { id: "dec2", label: "0.25" },
                            { id: "dec3", label: "0.75" }
                        ]
                    }
                };

                this.showDragDropMatching(testQuestion);
                this.updateButtonStatus();
            }

            // Use the same normalization logic as the real assessment
            showDragDropMatching(question) {
                const draggableItemsContainer = document.getElementById('draggable-items');
                const dropZonesContainer = document.getElementById('drop-zones');

                // Get configuration from question - handle multiple formats
                let config = question.config || question.dragDropConfig || {};
                let items = config.items || [];
                let targets = config.targets || config.zones || [];

                // Normalize items to extract text content
                const normalizedItems = items.map(item => {
                    if (typeof item === 'string') {
                        return item;
                    } else if (typeof item === 'object' && item.text) {
                        return item.text;
                    } else if (typeof item === 'object' && item.id) {
                        return item.id;
                    } else {
                        console.warn('Unknown item format:', item);
                        return String(item);
                    }
                });

                // Normalize targets to extract label content
                const normalizedTargets = targets.map(target => {
                    if (typeof target === 'string') {
                        return target;
                    } else if (typeof target === 'object' && target.label) {
                        return target.label;
                    } else if (typeof target === 'object' && target.id) {
                        return target.id;
                    } else {
                        console.warn('Unknown target format:', target);
                        return String(target);
                    }
                });

                console.log('Normalized items:', normalizedItems);
                console.log('Normalized targets:', normalizedTargets);

                this.dragDropState = {
                    items: normalizedItems,
                    targets: normalizedTargets,
                    correctPairs: [['1/2', '0.5'], ['1/4', '0.25'], ['3/4', '0.75']],
                    matches: new Map(),
                    completed: false
                };

                // Create draggable items
                normalizedItems.forEach((item, index) => {
                    const draggableItem = document.createElement('div');
                    draggableItem.className = 'draggable-item';
                    draggableItem.textContent = item; // This should now show the actual text, not "[object Object]"
                    draggableItem.draggable = true;
                    draggableItem.dataset.itemValue = item;
                    draggableItem.dataset.itemIndex = index;
                    draggableItemsContainer.appendChild(draggableItem);
                });

                // Create drop zones
                normalizedTargets.forEach((target, index) => {
                    const dropZone = document.createElement('div');
                    dropZone.className = 'drop-zone';
                    dropZone.dataset.targetValue = target;
                    dropZone.dataset.targetIndex = index;
                    dropZone.innerHTML = `
                        <div class="drop-zone-label">${target}</div>
                        <div class="drop-zone-content">Drop here</div>
                    `;
                    dropZonesContainer.appendChild(dropZone);
                });

                this.setupHTML5DragDrop();
            }
            
            setupHTML5DragDrop() {
                const draggableItems = document.querySelectorAll('.draggable-item');
                const dropZones = document.querySelectorAll('.drop-zone');
                
                // Set up draggable items
                draggableItems.forEach(item => {
                    item.addEventListener('dragstart', (e) => {
                        e.dataTransfer.setData('text/plain', item.dataset.itemValue);
                        item.classList.add('dragging');
                    });
                    
                    item.addEventListener('dragend', (e) => {
                        item.classList.remove('dragging');
                    });
                });
                
                // Set up drop zones
                dropZones.forEach(zone => {
                    zone.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        zone.classList.add('drag-over');
                    });
                    
                    zone.addEventListener('dragleave', (e) => {
                        zone.classList.remove('drag-over');
                    });
                    
                    zone.addEventListener('drop', (e) => {
                        e.preventDefault();
                        const dropZone = e.target.closest('.drop-zone');
                        dropZone.classList.remove('drag-over');
                        
                        const draggedValue = e.dataTransfer.getData('text/plain');
                        const draggedElement = document.querySelector(`[data-item-value="${draggedValue}"]`);
                        
                        if (draggedElement && dropZone) {
                            this.handleDrop(draggedElement, dropZone);
                        }
                    });
                });
            }
            
            handleDrop(draggedElement, dropZone) {
                const itemValue = draggedElement.dataset.itemValue;
                const targetValue = dropZone.dataset.targetValue;
                
                console.log('Drop:', { itemValue, targetValue });
                
                // Store the match
                this.dragDropState.matches.set(itemValue, targetValue);
                
                // Update UI
                dropZone.classList.add('filled');
                dropZone.querySelector('.drop-zone-content').textContent = itemValue;
                draggedElement.style.display = 'none';
                
                // Check if match is correct
                const isCorrect = this.checkDragDropMatch(itemValue, targetValue);
                if (isCorrect) {
                    dropZone.classList.add('correct');
                } else {
                    dropZone.classList.add('incorrect');
                }
                
                // Check completion
                this.checkDragDropCompletion();
            }
            
            checkDragDropMatch(itemValue, targetValue) {
                return this.dragDropState.correctPairs.some(pair =>
                    pair[0] === itemValue && pair[1] === targetValue
                );
            }
            
            checkDragDropCompletion() {
                const totalItems = this.dragDropState.items.length;
                const matchedItems = this.dragDropState.matches.size;
                
                console.log(`Progress: ${matchedItems}/${totalItems}`);
                
                // Enable next button if at least one match is made
                const hasMatches = matchedItems > 0;
                const nextBtn = document.getElementById('next-question-btn');
                
                if (nextBtn) {
                    nextBtn.disabled = !hasMatches;
                    this.updateButtonStatus();
                    console.log(`Next button ${hasMatches ? 'enabled' : 'disabled'}`);
                }
                
                if (matchedItems === totalItems) {
                    let correctMatches = 0;
                    this.dragDropState.matches.forEach((targetValue, itemValue) => {
                        if (this.checkDragDropMatch(itemValue, targetValue)) {
                            correctMatches++;
                        }
                    });
                    
                    if (correctMatches === totalItems) {
                        console.log('🎉 All matches correct!');
                        this.dragDropState.completed = true;
                    }
                }
            }
            
            updateButtonStatus() {
                const nextBtn = document.getElementById('next-question-btn');
                const statusDiv = document.getElementById('button-status');
                
                if (nextBtn.disabled) {
                    statusDiv.textContent = 'Button Status: DISABLED';
                    statusDiv.className = 'status disabled';
                } else {
                    statusDiv.textContent = 'Button Status: ENABLED';
                    statusDiv.className = 'status enabled';
                }
            }
        }
        
        // Initialize test when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new DragDropTest();
        });
        
        // Add click handler for next button
        document.getElementById('next-question-btn').addEventListener('click', () => {
            alert('Next Question button clicked! ✅');
        });
    </script>
</body>
</html>
