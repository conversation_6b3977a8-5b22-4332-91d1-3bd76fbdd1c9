/**
 * Interactive Mathematics Assessment Styling
 * Casio Calculator-inspired design theme
 * Colors: #1547bb (primary blue), #121c41 (dark blue)
 */

/* ============================================================================
   COMMON INTERACTIVE ELEMENTS
   ============================================================================ */

/* Eligibility Check Button */
.eligibility-btn {
  background: #1547bb;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 0.5rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.eligibility-btn:hover:not(:disabled) {
  background: #121c41;
  transform: translateY(-1px);
}

.eligibility-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.eligibility-btn .btn-loading {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.eligibility-btn .btn-loading::before {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Progress Display */
.progress-overview {
  background: #f9fafb;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #1547bb;
}

.progress-overview h4 {
  color: #374151;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.progress-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1547bb;
}

/* New User Message */
.new-user-message {
  background: linear-gradient(135deg, #1547bb 0%, #121c41 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
}

.new-user-message h4 {
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.new-user-message p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

/* Level Details */
.level-details {
  margin-top: 0.5rem;
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.level-badge {
  background: #1547bb;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Eligibility Note */
.eligibility-note {
  background: #f9fafb;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  border-left: 3px solid #1547bb;
}

.eligibility-note p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
}

.interactive-question {
  background: #ffffff;
  border: 2px solid #1547bb;
  border-radius: 12px;
  padding: 20px;
  margin: 15px 0;
  box-shadow: 0 4px 12px rgba(21, 71, 187, 0.1);
  transition: all 0.3s ease;
  overflow-x: auto; /* Allow horizontal scrolling if needed */
}

.interactive-question:hover {
  box-shadow: 0 6px 20px rgba(21, 71, 187, 0.15);
  transform: translateY(-2px);
}

.interactive-canvas {
  border: 4px solid #000000;
  border-radius: 8px;
  background: #ffffff;
  cursor: crosshair;
  display: block;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.interactive-canvas:focus {
  outline: 3px solid #0066cc;
  outline-offset: 2px;
}

.reset-btn, .check-btn {
  background: #000000;
  color: #ffffff;
  border: 3px solid #ffffff;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.reset-btn:hover, .check-btn:hover {
  background: #333333;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.reset-btn:active, .check-btn:active {
  transform: translateY(0);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
  background: #000000;
}

.reset-btn:focus, .check-btn:focus {
  outline: 3px solid #0066cc;
  outline-offset: 2px;
}

.btn-icon {
  font-size: 16px;
  font-weight: bold;
}

.input-hint {
  color: #666;
  font-size: 14px;
  font-style: italic;
  text-align: center;
  margin-top: 12px;
  padding: 8px;
  background: #f8f9ff;
  border-radius: 6px;
  border-left: 4px solid #1547bb;
}

.question-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 15px;
  padding: 10px;
  background: #f0f4ff;
  border-radius: 8px;
  border: 1px solid #e0e6ff;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ccc;
  transition: all 0.3s ease;
}

.status-indicator.active {
  background: #1547bb;
  box-shadow: 0 0 8px rgba(21, 71, 187, 0.4);
}

.status-indicator.complete {
  background: #28a745;
  box-shadow: 0 0 8px rgba(40, 167, 69, 0.4);
}

.status-text {
  font-size: 14px;
  font-weight: 600;
  color: #121c41;
}

.completion-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s ease;
}

.completion-indicator.show {
  opacity: 1;
  transform: scale(1);
}

.interactive-question {
  position: relative;
}

/* ============================================================================
   NUMBER LINE SLIDERS
   ============================================================================ */

.number-line-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 25px;
  background: #ffffff;
  border-radius: 10px;
  border: 3px solid #000000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.number-line-track {
  position: relative;
  width: 100%;
  max-width: 400px;
  height: 12px;
  background: #ffffff;
  border: 3px solid #000000;
  border-radius: 6px;
  cursor: pointer;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.number-line-handle {
  position: absolute;
  top: -12px;
  width: 36px;
  height: 36px;
  background: #000000;
  border: 4px solid #ffffff;
  border-radius: 50%;
  cursor: grab;
  transform: translateX(-50%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.number-line-handle:hover {
  transform: translateX(-50%) scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
  background: #333333;
}

.number-line-handle:active {
  cursor: grabbing;
  transform: translateX(-50%) scale(0.95);
  background: #000000;
}

.number-line-handle:focus {
  outline: 3px solid #0066cc;
  outline-offset: 2px;
}

.number-line-labels {
  display: flex;
  justify-content: space-between;
  width: 100%;
  max-width: 400px;
  font-size: 16px;
  color: #000000;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.number-line-value {
  background: #000000;
  color: #ffffff;
  padding: 15px 25px;
  border-radius: 30px;
  font-size: 20px;
  font-weight: 900;
  min-width: 90px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  border: 3px solid #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
  letter-spacing: 1px;
}

/* ============================================================================
   DRAG AND DROP MATCHING
   ============================================================================ */

.matching-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  padding: 25px;
  background: #ffffff;
  border-radius: 10px;
  border: 3px solid #000000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.draggable-items, .drop-zones {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.draggable-item {
  background: #ffffff;
  border: 3px solid #000000;
  border-radius: 8px;
  padding: 15px 20px;
  cursor: grab;
  text-align: center;
  font-size: 18px;
  font-weight: 700;
  color: #000000;
  transition: all 0.2s ease;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.draggable-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
  background: #f8f9fa;
  border-color: #333333;
}

.draggable-item.dragging {
  opacity: 0.8;
  transform: rotate(3deg) scale(1.05);
  cursor: grabbing;
  z-index: 1000;
  border-color: #0066cc;
  box-shadow: 0 8px 20px rgba(0, 102, 204, 0.4);
}

.draggable-item.selected {
  background: #000000;
  color: #ffffff;
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
  border-color: #ffffff;
}

.draggable-item:focus {
  outline: 3px solid #0066cc;
  outline-offset: 2px;
}

.drop-zone {
  background: #ffffff;
  border: 3px dashed #000000;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.drop-zone.drag-over {
  border-color: #0066cc;
  background: #e6f3ff;
  transform: scale(1.03);
  border-style: solid;
  box-shadow: 0 4px 12px rgba(0, 102, 204, 0.3);
}

.drop-zone.filled {
  background: #d4edda;
  border-color: #155724;
  border-style: solid;
  border-width: 3px;
  box-shadow: 0 3px 8px rgba(21, 87, 36, 0.2);
}

.drop-zone-label {
  font-weight: 700;
  color: #000000;
  margin-bottom: 10px;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.drop-zone-content {
  color: #666;
  font-style: italic;
  font-size: 13px;
}

.drop-zone.filled .drop-zone-content {
  color: #121c41;
  font-style: normal;
  font-weight: 600;
}

/* ============================================================================
   VISUAL CALCULATOR
   ============================================================================ */

#visual-calculator {
  overflow-x: auto; /* Allow horizontal scrolling for calculator */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on mobile */
}

.calculator-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 15px;
  background: #f8f9ff;
  border-radius: 10px;
  border: 1px solid #e0e6ff;
  max-width: 350px;
  margin: 0 auto;
  max-height: 70vh;
  overflow-y: auto;
}

.calculator-display {
  background: #000000;
  color: #ffffff;
  border-radius: 8px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  border: 2px solid #ffffff;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.5), 0 2px 6px rgba(0, 0, 0, 0.2);
}

.calculation-steps {
  min-height: 60px;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 10px;
  border-bottom: 1px solid #ffffff;
  padding-bottom: 10px;
  color: #ffffff;
  font-weight: 600;
}

.current-display {
  font-size: 22px;
  font-weight: 700;
  text-align: right;
  min-height: 35px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.calculator-keypad {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 6px;
  background: #e8e8e8;
  padding: 10px;
  border-radius: 8px;
  border: 2px solid #ccc;
}

.calc-btn {
  background: #ffffff;
  border: 2px solid #000000;
  border-radius: 6px;
  padding: 12px 8px;
  font-size: 16px;
  font-weight: 700;
  font-family: 'Arial', sans-serif;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #000000;
  min-height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.calc-btn:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  border-color: #333333;
}

.calc-btn:active {
  transform: translateY(0);
  box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.3);
  background: #e0e0e0;
}

.calc-btn:focus {
  outline: 3px solid #0066cc;
  outline-offset: 2px;
}

.calc-btn.operator {
  background: #000000;
  color: #ffffff;
  border-color: #ffffff;
  font-weight: 900;
}

.calc-btn.operator:hover {
  background: #333333;
  color: #ffffff;
  border-color: #ffffff;
}

.calc-btn.equals {
  background: #155724;
  color: #ffffff;
  border-color: #ffffff;
  font-weight: 900;
}

.calc-btn.equals:hover {
  background: #1e7e34;
  color: #ffffff;
  border-color: #ffffff;
}

.calc-btn.clear {
  background: #721c24;
  color: #ffffff;
  border-color: #ffffff;
  font-weight: 900;
}

.calc-btn.clear:hover {
  background: #dc3545;
  color: #ffffff;
  border-color: #ffffff;
}

.calculator-controls {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.step-btn {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(23, 162, 184, 0.3);
}

.step-btn:hover {
  background: linear-gradient(135deg, #138496, #17a2b8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
}

.step-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* ============================================================================
   NUMBER BONDS - ACCESSIBILITY ENHANCED
   ============================================================================ */

.number-bonds-container {
  display: flex;
  flex-direction: column;
  gap: 25px;
  padding: 25px;
  background: #ffffff;
  border-radius: 12px;
  border: 2px solid #0d3a8a;
  max-width: 600px;
  margin: 0 auto;
  box-shadow: 0 2px 8px rgba(13, 58, 138, 0.1);
}

.bonds-visual-container {
  background: #ffffff;
  border-radius: 10px;
  padding: 20px;
  border: 2px solid #0d3a8a;
  display: flex;
  justify-content: center;
  align-items: center;
}

.bond-equation {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 20px;
  font-weight: 700;
  color: #000000;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.bond-visual {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  min-height: 60px;
  background: #ffffff !important;
  border: 3px solid #000000 !important;
  border-radius: 8px;
  padding: 10px;
  font-size: 20px !important;
  font-weight: 900 !important;
  color: #000000 !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.bond-visual.missing {
  border-style: dashed !important;
  background: #ffffff !important;
  border-color: #dc3545 !important;
  border-width: 4px !important;
}

.missing-placeholder {
  font-size: 32px !important;
  color: #dc3545 !important;
  font-weight: 900 !important;
  text-shadow: 0 2px 4px rgba(220, 53, 69, 0.3) !important;
  background: transparent !important;
}

.bond-operator, .bond-equals {
  font-size: 32px !important;
  font-weight: 900 !important;
  color: #000000 !important;
  min-width: 50px;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  background: transparent !important;
}

.bond-result {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  min-height: 60px;
  background: #d4edda !important;
  border: 3px solid #155724 !important;
  border-radius: 8px;
  padding: 10px;
  font-size: 22px !important;
  font-weight: 900 !important;
  color: #000000 !important;
  box-shadow: 0 3px 6px rgba(21, 87, 36, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Visual representations - ACCESSIBILITY ENHANCED */
.circles-container, .blocks-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  justify-content: center;
  align-items: center;
  max-width: 200px;
  padding: 8px;
  background: #ffffff;
  border: 2px solid #000000;
  border-radius: 6px;
}

.visual-circle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #000000;
  border: 2px solid #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.visual-block {
  width: 16px;
  height: 16px;
  background: #000000;
  border: 2px solid #ffffff;
  border-radius: 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.number-line-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background: #ffffff;
  border: 2px solid #000000;
  border-radius: 6px;
}

/* Removed duplicate .number-line-value - using the one defined in NUMBER LINE SLIDERS section */

.number-line-bar {
  height: 12px;
  background: #000000;
  border: 2px solid #ffffff;
  border-radius: 6px;
  min-width: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.number-display {
  font-size: 18px;
  font-weight: 900;
  color: #000000;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Input area - ACCESSIBILITY ENHANCED */
.bonds-input-container {
  background: #ffffff;
  border-radius: 10px;
  padding: 20px;
  border: 2px solid #000000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.bond-input-area {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.bond-prompt {
  font-size: 20px;
  font-weight: 700;
  color: #000000;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.bond-input {
  width: 100px;
  padding: 15px;
  border: 4px solid #000000 !important;
  border-radius: 8px;
  font-size: 22px !important;
  font-weight: 900 !important;
  text-align: center;
  color: #000000 !important;
  background: #ffffff !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.bond-input:focus {
  outline: 4px solid #0066cc !important;
  outline-offset: 3px;
  border-color: #0066cc !important;
  background: #ffffff !important;
  box-shadow: 0 0 16px rgba(0, 102, 204, 0.5) !important;
}

.check-btn {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
}

.check-btn:hover {
  background: linear-gradient(135deg, #1e7e34, #28a745);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

/* Found pairs display */
.found-pairs {
  margin-top: 20px;
  padding: 15px;
  background: #f0f8ff;
  border-radius: 8px;
  border: 1px solid #b3d9ff;
}

.found-pairs h5 {
  margin: 0 0 10px 0;
  color: #121c41;
  font-size: 14px;
  font-weight: 600;
}

.pairs-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.found-pair {
  background: #28a745;
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
}

/* Controls */
.bonds-controls-container {
  display: flex;
  justify-content: center;
  gap: 12px;
}

/* Feedback */
.bonds-feedback-display {
  padding: 12px;
  border-radius: 6px;
  margin-top: 15px;
  text-align: center;
  font-weight: 600;
  display: none;
}

.bonds-feedback-display.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.bonds-feedback-display.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.bonds-feedback-display.warning {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

/* ============================================================================
   STEP-BY-STEP GUIDED PROBLEMS
   ============================================================================ */

.guided-problem-container {
  background: #f8f9ff;
  border-radius: 10px;
  border: 1px solid #e0e6ff;
  padding: 20px;
}

.current-step {
  margin-bottom: 20px;
}

.step-instruction {
  color: #121c41;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  padding: 12px;
  background: #ffffff;
  border-radius: 8px;
  border-left: 4px solid #1547bb;
}

.step-input-container {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 15px;
}

.step-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #1547bb;
  border-radius: 8px;
  font-size: 16px;
  font-family: 'Courier New', monospace;
  background: #ffffff;
}

.step-input:focus {
  outline: none;
  border-color: #0d3a8a;
  box-shadow: 0 0 0 3px rgba(21, 71, 187, 0.1);
}

.step-feedback {
  padding: 10px;
  border-radius: 6px;
  font-weight: 600;
  margin-bottom: 10px;
}

.step-feedback.correct {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.step-feedback.incorrect {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.step-hint {
  background: #fff3cd;
  color: #856404;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #ffeaa7;
  font-style: italic;
}

.progress-indicator {
  text-align: center;
  color: #666;
  font-size: 14px;
  font-weight: 600;
  padding: 10px;
  background: #ffffff;
  border-radius: 20px;
  border: 1px solid #e0e6ff;
}

/* ============================================================================
   COORDINATE PLOTTING - ACCESSIBILITY ENHANCED
   ============================================================================ */

.coordinate-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 25px;
  background: #ffffff;
  border-radius: 10px;
  border: 3px solid #000000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.coordinate-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 400px;
  gap: 15px;
}

.coordinate-display {
  background: #000000;
  color: #ffffff;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
  border: 2px solid #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* ============================================================================
   RATIO SLIDERS
   ============================================================================ */

.ratio-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: #f8f9ff;
  border-radius: 10px;
  border: 1px solid #e0e6ff;
}

.sliders-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.ratio-slider-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ratio-slider-label {
  font-weight: 600;
  color: #121c41;
  font-size: 14px;
}

.ratio-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #e0e6ff;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.ratio-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1547bb, #0d3a8a);
  border: 2px solid white;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(21, 71, 187, 0.4);
}

.ratio-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1547bb, #0d3a8a);
  border: 2px solid white;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(21, 71, 187, 0.4);
}

.ratio-display {
  text-align: center;
  background: #121c41;
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 18px;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  box-shadow: 0 3px 8px rgba(18, 28, 65, 0.3);
}

.ratio-controls {
  display: flex;
  justify-content: center;
}

/* ============================================================================
   EQUATION BUILDERS
   ============================================================================ */

.equation-builder-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: #f8f9ff;
  border-radius: 10px;
  border: 1px solid #e0e6ff;
}

.terms-container, .equation-workspace {
  background: #ffffff;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e0e6ff;
}

.terms-container h5, .equation-workspace h5 {
  margin: 0 0 12px 0;
  color: #121c41;
  font-weight: 600;
  font-size: 14px;
}

.terms-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.equation-term {
  background: linear-gradient(135deg, #ffffff, #f0f4ff);
  border: 2px solid #1547bb;
  border-radius: 6px;
  padding: 8px 12px;
  cursor: grab;
  font-weight: 600;
  color: #121c41;
  font-family: 'Courier New', monospace;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(21, 71, 187, 0.2);
}

.equation-term:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(21, 71, 187, 0.3);
}

.equation-term.dragging {
  opacity: 0.7;
  cursor: grabbing;
  z-index: 1000;
}

.equation-area {
  min-height: 60px;
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  background: #fafbff;
  transition: all 0.2s ease;
}

.equation-area.drag-over {
  border-color: #1547bb;
  background: #f0f4ff;
}

.equation-controls {
  display: flex;
  justify-content: center;
}

/* ============================================================================
   PATTERN COMPLETION
   ============================================================================ */

.pattern-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: #f8f9ff;
  border-radius: 10px;
  border: 1px solid #e0e6ff;
}

.pattern-display {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e0e6ff;
}

.pattern-input-area {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.pattern-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
  padding: 15px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e0e6ff;
}

.pattern-option {
  background: linear-gradient(135deg, #ffffff, #f0f4ff);
  border: 2px solid #1547bb;
  border-radius: 8px;
  padding: 12px 16px;
  cursor: pointer;
  font-weight: 600;
  color: #121c41;
  font-family: 'Courier New', monospace;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(21, 71, 187, 0.2);
  min-width: 60px;
  text-align: center;
}

.pattern-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(21, 71, 187, 0.3);
  background: linear-gradient(135deg, #f0f4ff, #e6ecff);
}

.pattern-option.selected {
  background: linear-gradient(135deg, #1547bb, #0d3a8a);
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(21, 71, 187, 0.4);
}

.pattern-answer-area {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e0e6ff;
}

.pattern-prompt {
  font-size: 16px;
  font-weight: 600;
  color: #121c41;
}

.pattern-answer-slot {
  min-width: 80px;
  min-height: 50px;
  border: 3px dashed #1547bb;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafbff;
  transition: all 0.3s ease;
  font-family: 'Courier New', monospace;
  font-weight: 600;
  font-size: 18px;
}

.pattern-answer-slot.filled {
  border-style: solid;
  background: linear-gradient(135deg, #e8f4fd, #d1e7dd);
  border-color: #28a745;
  color: #121c41;
}

.slot-placeholder {
  color: #999;
  font-size: 24px;
  font-weight: bold;
}

.pattern-controls {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.hint-btn {
  background: linear-gradient(135deg, #ffc107, #e0a800);
  color: #121c41;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(255, 193, 7, 0.3);
}

.hint-btn:hover {
  background: linear-gradient(135deg, #e0a800, #ffc107);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

@media (max-width: 768px) {
  .interactive-question {
    padding: 15px;
    margin: 10px 0;
  }

  .matching-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .number-line-container {
    padding: 15px;
  }

  .step-input-container {
    flex-direction: column;
    align-items: stretch;
  }

  .interactive-canvas {
    max-width: 100%;
    height: auto;
  }

  .pattern-options {
    justify-content: center;
  }

  .pattern-answer-area {
    flex-direction: column;
    gap: 10px;
  }

  .coordinate-controls {
    flex-direction: column;
    gap: 10px;
  }

  .equation-builder-container {
    padding: 15px;
  }

  .terms-list {
    justify-content: center;
  }

  .calculator-container {
    padding: 10px;
    max-width: 100%;
    max-height: 80vh;
    gap: 10px;
  }

  .calculator-display {
    padding: 10px;
  }

  .calculation-steps {
    min-height: 40px;
    font-size: 12px;
    margin-bottom: 8px;
    padding-bottom: 8px;
  }

  .current-display {
    font-size: 18px;
    min-height: 30px;
  }

  .calculator-keypad {
    grid-template-columns: repeat(4, 1fr);
    gap: 4px;
    padding: 8px;
  }

  .calc-btn {
    padding: 8px 6px;
    font-size: 14px;
    min-height: 35px;
    border-width: 1px;
  }
}

/* Extra small screens (phones in portrait) */
@media (max-width: 480px) {
  .calculator-container {
    padding: 8px;
    max-width: 100%;
    max-height: 85vh;
    gap: 8px;
  }

  .calculator-display {
    padding: 8px;
  }

  .calculation-steps {
    min-height: 35px;
    font-size: 11px;
    margin-bottom: 6px;
    padding-bottom: 6px;
  }

  .current-display {
    font-size: 16px;
    min-height: 25px;
  }

  .calculator-keypad {
    gap: 3px;
    padding: 6px;
  }

  .calc-btn {
    padding: 6px 4px;
    font-size: 12px;
    min-height: 30px;
    border-width: 1px;
  }

  .calculator-controls {
    gap: 8px;
  }

  .step-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
}

/* ============================================================================
   QUESTION INSTRUCTIONS
   ============================================================================ */

.question-instructions {
  margin: 1rem 0;
  padding: 0.75rem 1rem;
  background: #f8f9ff;
  border: 1px solid #e1e5f2;
  border-radius: 8px;
  border-left: 4px solid #1547bb;
}

.instruction-text {
  margin: 0;
  font-size: 0.9rem;
  color: #374151;
  line-height: 1.4;
  font-weight: 500;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .question-instructions {
    background: #1e293b;
    border-color: #334155;
    border-left-color: #3b82f6;
  }

  .instruction-text {
    color: #e2e8f0;
  }
}
