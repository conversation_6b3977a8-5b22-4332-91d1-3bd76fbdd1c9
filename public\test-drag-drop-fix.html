<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Drag-Drop Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .draggable-item {
            display: inline-block;
            padding: 10px 15px;
            margin: 5px;
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 5px;
            cursor: grab;
            user-select: none;
            font-weight: bold;
        }
        
        .draggable-item:hover {
            background: #bbdefb;
        }
        
        .draggable-item.dragging {
            opacity: 0.5;
        }
        
        .drop-zone {
            display: inline-block;
            width: 120px;
            height: 80px;
            margin: 10px;
            border: 2px dashed #ccc;
            border-radius: 5px;
            text-align: center;
            vertical-align: top;
            position: relative;
            background: #fafafa;
        }
        
        .drop-zone.drag-over {
            border-color: #2196f3;
            background: #e3f2fd;
        }
        
        .drop-zone.correct {
            border-color: #4caf50;
            background: #e8f5e8;
        }
        
        .drop-zone.incorrect {
            border-color: #f44336;
            background: #ffebee;
        }
        
        .drop-zone-label {
            font-weight: bold;
            padding: 5px;
            background: #f0f0f0;
            border-radius: 3px 3px 0 0;
        }
        
        .drop-zone-content {
            padding: 10px 5px;
            font-size: 14px;
            color: #666;
        }
        
        .next-button {
            padding: 10px 20px;
            background: #4caf50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px 0;
        }
        
        .next-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        .status.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        
        .status.info {
            background: #e3f2fd;
            color: #1565c0;
            border: 1px solid #2196f3;
        }
    </style>
</head>
<body>
    <h1>🧪 Drag-Drop Fix Test</h1>
    <p>This page tests the fixes for:</p>
    <ul>
        <li><strong>Issue 1:</strong> "[object Object]" display in draggable items</li>
        <li><strong>Issue 2:</strong> Next Question button not enabling after interaction</li>
    </ul>
    
    <div class="test-section">
        <h2>Test: Object Format Data Structure</h2>
        <p>This simulates the exact data structure that was causing "[object Object]" display:</p>
        
        <div id="status-1" class="status info">Ready to test - drag items to zones</div>
        
        <div class="drag-drop-area">
            <div id="draggable-items-1">
                <!-- Items will be populated by JavaScript -->
            </div>
            
            <div id="drop-zones-1">
                <!-- Zones will be populated by JavaScript -->
            </div>
        </div>
        
        <button id="next-btn-1" class="next-button" disabled>Next Question</button>
    </div>
    
    <script>
        // Test data with object format (the problematic format)
        const testQuestion = {
            type: "drag-drop",
            question: "Match each fraction to its decimal equivalent.",
            dragDropConfig: {
                items: [
                    { id: "frac1", text: "1/2" },
                    { id: "frac2", text: "1/4" },
                    { id: "frac3", text: "3/4" }
                ],
                zones: [
                    { id: "dec1", label: "0.5" },
                    { id: "dec2", label: "0.25" },
                    { id: "dec3", label: "0.75" }
                ]
            }
        };
        
        class DragDropFixTest {
            constructor() {
                this.initializeTest();
            }
            
            initializeTest() {
                // Use the same normalization logic as the fixed assessment
                const config = testQuestion.dragDropConfig || {};
                const items = config.items || [];
                const zones = config.zones || [];
                
                // Normalize items (this is the fix for "[object Object]")
                const normalizedItems = items.map(item => {
                    if (typeof item === 'string') {
                        return item;
                    } else if (typeof item === 'object' && item.text) {
                        return item.text; // Extract text property
                    } else if (typeof item === 'object' && item.id) {
                        return item.id;
                    } else {
                        return String(item);
                    }
                });
                
                // Normalize zones
                const normalizedZones = zones.map(zone => {
                    if (typeof zone === 'string') {
                        return zone;
                    } else if (typeof zone === 'object' && zone.label) {
                        return zone.label; // Extract label property
                    } else if (typeof zone === 'object' && zone.id) {
                        return zone.id;
                    } else {
                        return String(zone);
                    }
                });
                
                console.log('Original items:', items);
                console.log('Normalized items:', normalizedItems);
                console.log('Original zones:', zones);
                console.log('Normalized zones:', normalizedZones);
                
                this.createDragDropElements(normalizedItems, normalizedZones);
                this.setupDragDrop();
            }
            
            createDragDropElements(items, zones) {
                const itemsContainer = document.getElementById('draggable-items-1');
                const zonesContainer = document.getElementById('drop-zones-1');
                
                // Create draggable items - should show actual text, not "[object Object]"
                items.forEach((item, index) => {
                    const div = document.createElement('div');
                    div.className = 'draggable-item';
                    div.textContent = item; // This should now show "1/2", "1/4", "3/4"
                    div.draggable = true;
                    div.dataset.value = item;
                    itemsContainer.appendChild(div);
                });
                
                // Create drop zones
                zones.forEach((zone, index) => {
                    const div = document.createElement('div');
                    div.className = 'drop-zone';
                    div.dataset.accept = zone;
                    div.innerHTML = `
                        <div class="drop-zone-label">${zone}</div>
                        <div class="drop-zone-content">Drop here</div>
                    `;
                    zonesContainer.appendChild(div);
                });
            }
            
            setupDragDrop() {
                const items = document.querySelectorAll('.draggable-item');
                const zones = document.querySelectorAll('.drop-zone');
                const nextBtn = document.getElementById('next-btn-1');
                const status = document.getElementById('status-1');
                
                // Drag start
                items.forEach(item => {
                    item.addEventListener('dragstart', (e) => {
                        e.dataTransfer.setData('text/plain', e.target.dataset.value);
                        e.target.classList.add('dragging');

                        // Enable Next Question button immediately when user starts dragging
                        nextBtn.disabled = false;
                        status.className = 'status info';
                        status.textContent = '✅ Next Question button enabled! You can now proceed or continue matching.';
                        console.log('Next button enabled on drag start');
                    });

                    item.addEventListener('dragend', (e) => {
                        e.target.classList.remove('dragging');
                    });
                });
                
                // Drop zones
                zones.forEach(zone => {
                    zone.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        zone.classList.add('drag-over');
                    });
                    
                    zone.addEventListener('dragleave', () => {
                        zone.classList.remove('drag-over');
                    });
                    
                    zone.addEventListener('drop', (e) => {
                        e.preventDefault();
                        zone.classList.remove('drag-over');
                        
                        const draggedValue = e.dataTransfer.getData('text/plain');
                        const acceptedValue = zone.dataset.accept;
                        
                        // Update zone content
                        const content = zone.querySelector('.drop-zone-content');
                        content.textContent = draggedValue;
                        
                        // Check if correct
                        const correctPairs = {
                            '0.5': '1/2',
                            '0.25': '1/4', 
                            '0.75': '3/4'
                        };
                        
                        if (correctPairs[acceptedValue] === draggedValue) {
                            zone.classList.add('correct');
                            status.className = 'status success';
                            status.textContent = `✅ Correct! ${draggedValue} = ${acceptedValue}`;
                        } else {
                            zone.classList.add('incorrect');
                            status.className = 'status error';
                            status.textContent = `❌ Try again! ${draggedValue} ≠ ${acceptedValue}`;
                        }
                        
                        // Enable Next Question button on ANY interaction (this is the second fix)
                        nextBtn.disabled = false;
                        console.log('Next button enabled after drag-drop interaction');
                    });
                });
                
                // Next button
                nextBtn.addEventListener('click', () => {
                    status.className = 'status success';
                    status.textContent = '🎉 Test completed! Both fixes are working correctly.';
                });
            }
        }
        
        // Initialize test when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new DragDropFixTest();
        });
    </script>
</body>
</html>
